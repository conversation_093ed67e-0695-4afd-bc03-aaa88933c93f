# RPG数据采集系统部署运维指南

## 1. 系统环境准备

### 1.1 硬件要求

| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| CPU | 双核 2.0GHz | 四核 2.5GHz+ | 支持浏览器自动化 |
| 内存 | 4GB | 8GB+ | 浏览器进程占用较多内存 |
| 存储 | 20GB | 50GB+ | 日志和缓存数据 |
| 网络 | 10Mbps | 100Mbps+ | 稳定的互联网连接 |
| 串口 | RS485转USB | 工业级RS485卡 | 数据传输接口 |

### 1.2 软件环境

#### 1.2.1 操作系统支持
- **Linux**: Ubuntu 20.04+, CentOS 8+, Debian 11+
- **Windows**: Windows 10/11, Windows Server 2019+
- **macOS**: macOS 11+ (开发测试环境)

#### 1.2.2 Python环境
```bash
# 安装Python 3.8+
sudo apt update
sudo apt install python3.8 python3.8-pip python3.8-venv

# 创建虚拟环境
python3.8 -m venv rpg_collector_env
source rpg_collector_env/bin/activate

# 升级pip
pip install --upgrade pip
```

#### 1.2.3 系统依赖
```bash
# Ubuntu/Debian
sudo apt install -y \
    chromium-browser \
    chromium-chromedriver \
    libnss3-dev \
    libatk-bridge2.0-dev \
    libdrm2 \
    libxkbcommon0 \
    libgtk-3-0 \
    libgbm-dev

# CentOS/RHEL
sudo yum install -y \
    chromium \
    chromium-headless \
    nss \
    atk \
    at-spi2-atk \
    gtk3 \
    cups-libs \
    libdrm \
    libxkbcommon \
    libXcomposite \
    libXdamage \
    libXrandr \
    libgbm \
    libXss \
    libasound
```

## 2. 安装部署

### 2.1 源码部署

```bash
# 1. 克隆项目
git clone https://github.com/your-org/rpg-data-collector.git
cd rpg-data-collector

# 2. 创建并激活虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows

# 3. 安装Python依赖
pip install -r requirements.txt

# 4. 安装Playwright浏览器
playwright install chromium

# 5. 创建配置文件
cp config.example.yml config.yml
cp data-sources.example.yml data-sources.yml

# 6. 创建必要目录
mkdir -p logs data cache

# 7. 设置权限
chmod +x scripts/*.sh
```

### 2.2 Docker部署

#### 2.2.1 Dockerfile
```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    wget \
    gnupg \
    ca-certificates \
    fonts-liberation \
    libasound2 \
    libatk-bridge2.0-0 \
    libdrm2 \
    libgtk-3-0 \
    libnspr4 \
    libnss3 \
    libxss1 \
    libxtst6 \
    xdg-utils \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 安装Playwright
RUN playwright install chromium

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 collector && \
    chown -R collector:collector /app

USER collector

# 暴露端口（如果需要Web界面）
EXPOSE 8080

# 启动命令
CMD ["python", "main.py"]
```

#### 2.2.2 docker-compose.yml
```yaml
version: '3.8'

services:
  rpg-collector:
    build: .
    container_name: rpg-data-collector
    restart: unless-stopped
    volumes:
      - ./config.yml:/app/config.yml:ro
      - ./data-sources.yml:/app/data-sources.yml:ro
      - ./logs:/app/logs
      - ./data:/app/data
    devices:
      - "/dev/ttyUSB0:/dev/ttyUSB0"  # 串口设备映射
    environment:
      - PYTHONUNBUFFERED=1
      - TZ=Asia/Shanghai
    networks:
      - collector-network

  redis:
    image: redis:7-alpine
    container_name: rpg-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    networks:
      - collector-network

  monitoring:
    image: prom/prometheus:latest
    container_name: rpg-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    networks:
      - collector-network

volumes:
  redis-data:

networks:
  collector-network:
    driver: bridge
```

### 2.3 配置文件设置

#### 2.3.1 生产环境配置 (config.yml)
```yaml
system:
  name: "RPG数据采集系统"
  version: "1.0.0"
  environment: "production"
  debug: false

browser:
  headless: true
  timeout: 60000
  user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
  max_pages: 5
  
serial:
  port: "/dev/ttyUSB0"
  baudrate: 9600
  timeout: 2
  retry_attempts: 3
  
scheduler:
  max_workers: 3
  retry_attempts: 5
  retry_delay: 10
  max_task_runtime: 300
  
logging:
  level: "INFO"
  file: "logs/collector.log"
  max_size: "50MB"
  backup_count: 10
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

monitoring:
  enabled: true
  metrics_port: 8080
  health_check_interval: 30

cache:
  type: "redis"
  host: "localhost"
  port: 6379
  db: 0
  ttl: 3600
```

## 3. 系统服务配置

### 3.1 Systemd服务 (Linux)

#### 3.1.1 服务文件 (/etc/systemd/system/rpg-collector.service)
```ini
[Unit]
Description=RPG Data Collector Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=collector
Group=collector
WorkingDirectory=/opt/rpg-collector
Environment=PYTHONPATH=/opt/rpg-collector
ExecStart=/opt/rpg-collector/venv/bin/python main.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=rpg-collector

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/rpg-collector/logs /opt/rpg-collector/data

[Install]
WantedBy=multi-user.target
```

#### 3.1.2 服务管理命令
```bash
# 重新加载systemd配置
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable rpg-collector

# 启动服务
sudo systemctl start rpg-collector

# 查看服务状态
sudo systemctl status rpg-collector

# 查看日志
sudo journalctl -u rpg-collector -f

# 停止服务
sudo systemctl stop rpg-collector

# 重启服务
sudo systemctl restart rpg-collector
```

### 3.2 Windows服务

#### 3.2.1 使用NSSM安装Windows服务
```batch
# 下载并安装NSSM
# https://nssm.cc/download

# 安装服务
nssm install "RPG Data Collector" "C:\rpg-collector\venv\Scripts\python.exe" "C:\rpg-collector\main.py"

# 设置工作目录
nssm set "RPG Data Collector" AppDirectory "C:\rpg-collector"

# 设置日志
nssm set "RPG Data Collector" AppStdout "C:\rpg-collector\logs\service.log"
nssm set "RPG Data Collector" AppStderr "C:\rpg-collector\logs\error.log"

# 启动服务
nssm start "RPG Data Collector"
```

## 4. 监控和告警

### 4.1 健康检查脚本 (scripts/health_check.py)
```python
#!/usr/bin/env python3
import requests
import sys
import json
from datetime import datetime, timedelta

def check_system_health():
    """检查系统健康状态"""
    checks = {
        'service_running': check_service_status(),
        'serial_connection': check_serial_connection(),
        'recent_data': check_recent_data(),
        'disk_space': check_disk_space(),
        'memory_usage': check_memory_usage()
    }
    
    all_healthy = all(checks.values())
    
    result = {
        'timestamp': datetime.now().isoformat(),
        'healthy': all_healthy,
        'checks': checks
    }
    
    print(json.dumps(result, indent=2))
    return 0 if all_healthy else 1

def check_service_status():
    """检查服务状态"""
    try:
        response = requests.get('http://localhost:8080/health', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_serial_connection():
    """检查串口连接"""
    try:
        import serial
        ser = serial.Serial('/dev/ttyUSB0', 9600, timeout=1)
        ser.close()
        return True
    except:
        return False

def check_recent_data():
    """检查最近数据"""
    try:
        # 检查最近5分钟是否有数据
        with open('logs/collector.log', 'r') as f:
            lines = f.readlines()
            recent_lines = [line for line in lines[-100:] 
                          if 'successful' in line.lower()]
            return len(recent_lines) > 0
    except:
        return False

def check_disk_space():
    """检查磁盘空间"""
    import shutil
    total, used, free = shutil.disk_usage('/')
    free_percent = (free / total) * 100
    return free_percent > 10  # 至少10%空闲空间

def check_memory_usage():
    """检查内存使用"""
    import psutil
    memory = psutil.virtual_memory()
    return memory.percent < 90  # 内存使用率低于90%

if __name__ == '__main__':
    sys.exit(check_system_health())
```

### 4.2 Prometheus监控配置
```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'rpg-collector'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 4.3 告警规则 (monitoring/alert_rules.yml)
```yaml
groups:
  - name: rpg_collector_alerts
    rules:
      - alert: ServiceDown
        expr: up{job="rpg-collector"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "RPG数据采集服务停止运行"
          description: "服务已停止运行超过1分钟"

      - alert: HighMemoryUsage
        expr: process_resident_memory_bytes / 1024 / 1024 > 500
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "内存使用超过500MB"

      - alert: NoRecentData
        expr: increase(data_collection_total[5m]) == 0
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "最近5分钟无数据采集"
          description: "可能存在数据源问题"
```

## 5. 备份和恢复

### 5.1 备份脚本 (scripts/backup.sh)
```bash
#!/bin/bash

BACKUP_DIR="/backup/rpg-collector"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="rpg_collector_backup_${DATE}.tar.gz"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 停止服务
sudo systemctl stop rpg-collector

# 备份配置和数据
tar -czf "${BACKUP_DIR}/${BACKUP_FILE}" \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    /opt/rpg-collector/

# 启动服务
sudo systemctl start rpg-collector

# 清理旧备份（保留7天）
find $BACKUP_DIR -name "rpg_collector_backup_*.tar.gz" -mtime +7 -delete

echo "备份完成: ${BACKUP_DIR}/${BACKUP_FILE}"
```

### 5.2 恢复脚本 (scripts/restore.sh)
```bash
#!/bin/bash

if [ $# -ne 1 ]; then
    echo "用法: $0 <备份文件路径>"
    exit 1
fi

BACKUP_FILE=$1

# 停止服务
sudo systemctl stop rpg-collector

# 备份当前配置
cp -r /opt/rpg-collector /opt/rpg-collector.backup.$(date +%Y%m%d_%H%M%S)

# 恢复备份
tar -xzf $BACKUP_FILE -C /

# 重新安装依赖
cd /opt/rpg-collector
source venv/bin/activate
pip install -r requirements.txt

# 启动服务
sudo systemctl start rpg-collector

echo "恢复完成"
```

## 6. 故障排除

### 6.1 常见问题

#### 6.1.1 浏览器启动失败
```bash
# 检查浏览器依赖
ldd $(which chromium-browser)

# 安装缺失的依赖
sudo apt install -y libnss3 libatk-bridge2.0-0

# 检查权限
ls -la /usr/bin/chromium-browser
```

#### 6.1.2 串口连接问题
```bash
# 检查串口设备
ls -la /dev/ttyUSB*

# 检查权限
sudo usermod -a -G dialout $USER

# 测试串口
python3 -c "import serial; print(serial.Serial('/dev/ttyUSB0', 9600))"
```

#### 6.1.3 内存泄漏
```bash
# 监控内存使用
watch -n 5 'ps aux | grep python | grep main.py'

# 重启服务释放内存
sudo systemctl restart rpg-collector
```

### 6.2 日志分析

#### 6.2.1 日志级别说明
- **ERROR**: 系统错误，需要立即处理
- **WARNING**: 警告信息，可能影响功能
- **INFO**: 一般信息，正常运行状态
- **DEBUG**: 调试信息，详细执行过程

#### 6.2.2 常用日志查询
```bash
# 查看错误日志
grep "ERROR" logs/collector.log | tail -20

# 查看最近的数据采集
grep "successful" logs/collector.log | tail -10

# 统计错误频率
grep "ERROR" logs/collector.log | awk '{print $1}' | sort | uniq -c

# 实时监控日志
tail -f logs/collector.log | grep -E "(ERROR|WARNING)"
```

## 7. 性能优化

### 7.1 系统调优
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化网络参数
echo "net.core.rmem_max = 16777216" >> /etc/sysctl.conf
echo "net.core.wmem_max = 16777216" >> /etc/sysctl.conf
sysctl -p
```

### 7.2 应用优化
- 启用数据缓存减少重复采集
- 使用连接池管理浏览器实例
- 实现智能重试机制
- 优化数据序列化格式

这个部署运维指南涵盖了从环境准备到故障排除的完整流程，确保系统能够稳定可靠地运行。
