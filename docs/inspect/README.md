# RPG数据采集与转发系统

## 📋 项目概述

本项目实现了一个基于RPG（Robotic Process Automation）的自动化数据采集系统，通过浏览器自动化技术从Web页面采集数据，经过归一化处理后通过RS485串口转发到服务器进行后续处理。

## 🏗️ 系统架构

```mermaid
graph LR
    A[Web数据源] --> B[RPG采集引擎]
    B --> C[数据归一化]
    C --> D[RS485通信]
    D --> E[服务器处理]
    
    subgraph "核心组件"
        F[浏览器控制器]
        G[任务调度器]
        H[数据验证器]
        I[串口通信器]
    end
```

## ✨ 核心特性

- 🤖 **智能RPG引擎**: 基于Playwright的浏览器自动化
- 🔄 **自动化调度**: 支持多任务并发执行和智能重试
- 📊 **数据归一化**: 自动清洗、验证和格式转换
- 🔌 **RS485通信**: 稳定的串口数据传输
- 📈 **实时监控**: 完整的日志记录和性能监控
- 🛡️ **异常处理**: 多层次的错误处理和恢复机制
- 🔧 **配置驱动**: 灵活的配置文件管理
- 📦 **容器化部署**: 支持Docker和传统部署方式

## 🚀 快速开始

### 环境要求

- Python 3.8+
- Chrome/Chromium浏览器
- RS485转USB适配器
- Linux/Windows/macOS

### 安装步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd rpg-data-collector
```

2. **创建虚拟环境**
```bash
python3 -m venv venv
source venv/bin/activate  # Linux/macOS
# 或 venv\Scripts\activate  # Windows
```

3. **安装依赖**
```bash
pip install -r requirements.txt
playwright install chromium
```

4. **配置系统**
```bash
cp config.example.yml config.yml
cp data-sources.example.yml data-sources.yml
# 编辑配置文件
```

5. **启动系统**
```bash
python main.py
```

### Docker部署

```bash
# 构建镜像
docker build -t rpg-collector .

# 运行容器
docker-compose up -d
```

## 📁 项目结构

```
rpg-data-collector/
├── main.py                    # 主程序入口
├── browser_controller.py      # 浏览器控制模块
├── data_normalizer.py         # 数据归一化模块
├── rs485_communicator.py      # RS485通信模块
├── task_scheduler.py          # 任务调度模块
├── config.yml                 # 主配置文件
├── data-sources.yml           # 数据源配置
├── requirements.txt           # Python依赖
├── Dockerfile                 # Docker配置
├── docker-compose.yml         # Docker编排
├── docs/                      # 文档目录
│   └── inspect/              # 项目文档
│       ├── rpg-data-collection-system.md
│       ├── technical-implementation.md
│       ├── deployment-guide.md
│       └── README.md
├── scripts/                   # 脚本目录
│   ├── start.sh              # 启动脚本
│   ├── backup.sh             # 备份脚本
│   └── health_check.py       # 健康检查
├── logs/                      # 日志目录
├── data/                      # 数据目录
└── monitoring/                # 监控配置
    ├── prometheus.yml
    └── alert_rules.yml
```

## ⚙️ 配置说明

### 主配置文件 (config.yml)

```yaml
system:
  name: "RPG数据采集系统"
  version: "1.0.0"

browser:
  headless: true
  timeout: 30000
  user_agent: "Mozilla/5.0 (compatible; DataCollector/1.0)"

serial:
  port: "/dev/ttyUSB0"
  baudrate: 9600
  timeout: 1

scheduler:
  max_workers: 3
  retry_attempts: 3
  retry_delay: 5

logging:
  level: "INFO"
  file: "logs/collector.log"
```

### 数据源配置 (data-sources.yml)

```yaml
sources:
  - id: "weather_station_1"
    name: "气象站数据"
    url: "https://weather.example.com/station1"
    interval: 300
    selectors:
      temperature: ".temp-value"
      humidity: ".humidity-value"
    validation:
      temperature: {min: -40, max: 50}
      humidity: {min: 0, max: 100}
```

## 🔧 使用指南

### 添加新数据源

1. 在 `data-sources.yml` 中添加新的数据源配置
2. 定义CSS选择器或API端点
3. 设置数据验证规则
4. 重启系统加载新配置

### 自定义数据处理

```python
# 在 data_normalizer.py 中添加自定义处理逻辑
def custom_processor(self, field, value):
    if field == 'custom_field':
        # 自定义处理逻辑
        return processed_value
    return value
```

### 监控系统状态

```bash
# 查看系统状态
systemctl status rpg-collector

# 查看实时日志
tail -f logs/collector.log

# 健康检查
python scripts/health_check.py
```

## 📊 监控和告警

### Prometheus指标

- `data_collection_total`: 数据采集总数
- `data_collection_errors`: 采集错误数
- `serial_transmission_duration`: 串口传输耗时
- `browser_page_load_duration`: 页面加载耗时

### 告警规则

- 服务停止运行
- 内存使用率过高
- 最近无数据采集
- 串口通信失败

## 🛠️ 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome/Chromium安装
   - 验证系统依赖库
   - 确认用户权限

2. **串口连接问题**
   - 检查设备路径 `/dev/ttyUSB0`
   - 验证用户组权限 `dialout`
   - 测试串口通信

3. **数据采集失败**
   - 检查网络连接
   - 验证CSS选择器
   - 查看目标网站变化

### 日志分析

```bash
# 查看错误日志
grep "ERROR" logs/collector.log

# 统计采集成功率
grep -c "successful" logs/collector.log

# 监控实时状态
tail -f logs/collector.log | grep -E "(ERROR|SUCCESS)"
```

## 🔒 安全考虑

- 使用非特权用户运行服务
- 限制网络访问权限
- 加密敏感配置信息
- 定期更新依赖包
- 监控异常访问行为

## 📈 性能优化

- 启用浏览器缓存
- 使用连接池管理
- 实现智能重试策略
- 优化数据序列化
- 配置合适的并发数

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如果您遇到问题或有建议，请：

- 查看 [文档](docs/inspect/)
- 提交 [Issue](https://github.com/your-org/rpg-data-collector/issues)
- 联系维护团队

## 🗺️ 路线图

### v1.1 (计划中)
- [ ] Web管理界面
- [ ] 更多通信协议支持
- [ ] 机器学习数据预处理
- [ ] 分布式部署支持

### v1.2 (未来)
- [ ] 实时数据流处理
- [ ] 高级数据分析
- [ ] 移动端监控应用
- [ ] 云原生部署

## 📚 相关文档

- [系统设计方案](docs/inspect/rpg-data-collection-system.md)
- [技术实现指南](docs/inspect/technical-implementation.md)
- [部署运维指南](docs/inspect/deployment-guide.md)

---

**注意**: 本系统设计用于合法的数据采集场景，请确保遵守相关网站的使用条款和法律法规。
