# RPG数据采集系统技术实现指南

## 1. 核心模块实现

### 1.1 主控制器 (main.py)

```python
import asyncio
import logging
from datetime import datetime
from typing import Dict, List
import yaml
from dataclasses import dataclass

from browser_controller import <PERSON>rowserController
from data_normalizer import DataNormalizer
from rs485_communicator import RS485Communicator
from task_scheduler import TaskScheduler

@dataclass
class CollectionTask:
    id: str
    name: str
    url: str
    selectors: Dict[str, str]
    interval: int
    last_run: datetime = None

class RPGDataCollector:
    def __init__(self, config_path: str = "config.yml"):
        self.config = self._load_config(config_path)
        self.browser_controller = BrowserController(self.config['browser'])
        self.data_normalizer = DataNormalizer()
        self.rs485_comm = RS485Communicator(
            port=self.config['serial']['port'],
            baudrate=self.config['serial']['baudrate']
        )
        self.scheduler = TaskScheduler(self.config['scheduler'])
        self.tasks: List[CollectionTask] = []
        
        # 设置日志
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.config['logging']['file']),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def _load_config(self, config_path: str) -> dict:
        with open(config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    async def initialize(self):
        """初始化系统组件"""
        self.logger.info("正在初始化RPG数据采集系统...")
        
        # 初始化浏览器
        await self.browser_controller.initialize()
        
        # 加载数据源配置
        self._load_data_sources()
        
        # 初始化串口通信
        self.rs485_comm.connect()
        
        self.logger.info("系统初始化完成")
    
    def _load_data_sources(self):
        """加载数据源配置"""
        with open('data-sources.yml', 'r', encoding='utf-8') as f:
            sources_config = yaml.safe_load(f)
        
        for source in sources_config['sources']:
            task = CollectionTask(
                id=source['id'],
                name=source['name'],
                url=source['url'],
                selectors=source['selectors'],
                interval=source['interval']
            )
            self.tasks.append(task)
            self.logger.info(f"已加载数据源: {task.name}")
    
    async def run_collection_task(self, task: CollectionTask):
        """执行单个数据采集任务"""
        try:
            self.logger.info(f"开始执行任务: {task.name}")
            
            # 1. 浏览器数据采集
            raw_data = await self.browser_controller.extract_data(
                task.url, 
                task.selectors
            )
            
            # 2. 数据归一化
            normalized_data = self.data_normalizer.normalize(
                raw_data, 
                task.id
            )
            
            # 3. 添加元数据
            final_data = {
                'source_id': task.id,
                'source_name': task.name,
                'timestamp': datetime.now().isoformat(),
                'data': normalized_data
            }
            
            # 4. 通过RS485发送数据
            response = self.rs485_comm.send_data(final_data)
            
            if response['success']:
                self.logger.info(f"任务 {task.name} 执行成功")
                task.last_run = datetime.now()
            else:
                self.logger.error(f"任务 {task.name} 发送失败: {response['error']}")
                
        except Exception as e:
            self.logger.error(f"任务 {task.name} 执行异常: {str(e)}")
            raise
    
    async def start(self):
        """启动数据采集系统"""
        await self.initialize()
        
        self.logger.info("开始运行数据采集任务...")
        
        # 启动任务调度器
        await self.scheduler.start(self.tasks, self.run_collection_task)
    
    async def stop(self):
        """停止系统"""
        self.logger.info("正在停止系统...")
        
        await self.scheduler.stop()
        await self.browser_controller.close()
        self.rs485_comm.disconnect()
        
        self.logger.info("系统已停止")

if __name__ == "__main__":
    collector = RPGDataCollector()
    
    try:
        asyncio.run(collector.start())
    except KeyboardInterrupt:
        asyncio.run(collector.stop())
```

### 1.2 浏览器控制器 (browser_controller.py)

```python
import asyncio
from playwright.async_api import async_playwright, Browser, Page
from typing import Dict, Any
import logging

class BrowserController:
    def __init__(self, config: dict):
        self.config = config
        self.playwright = None
        self.browser: Browser = None
        self.page: Page = None
        self.logger = logging.getLogger(__name__)
    
    async def initialize(self):
        """初始化浏览器"""
        self.playwright = await async_playwright().start()
        
        self.browser = await self.playwright.chromium.launch(
            headless=self.config.get('headless', True),
            args=['--no-sandbox', '--disable-setuid-sandbox']
        )
        
        self.page = await self.browser.new_page()
        
        # 设置用户代理
        if 'user_agent' in self.config:
            await self.page.set_extra_http_headers({
                'User-Agent': self.config['user_agent']
            })
        
        self.logger.info("浏览器初始化完成")
    
    async def extract_data(self, url: str, selectors: Dict[str, str]) -> Dict[str, Any]:
        """从指定URL提取数据"""
        try:
            # 导航到目标页面
            await self.page.goto(url, timeout=self.config.get('timeout', 30000))
            
            # 等待页面加载完成
            await self.page.wait_for_load_state('networkidle')
            
            # 提取数据
            extracted_data = {}
            for field_name, selector in selectors.items():
                try:
                    element = await self.page.wait_for_selector(
                        selector, 
                        timeout=5000
                    )
                    if element:
                        text_content = await element.text_content()
                        extracted_data[field_name] = text_content.strip() if text_content else None
                    else:
                        extracted_data[field_name] = None
                        self.logger.warning(f"未找到选择器 {selector} 对应的元素")
                        
                except Exception as e:
                    self.logger.error(f"提取字段 {field_name} 时出错: {str(e)}")
                    extracted_data[field_name] = None
            
            self.logger.info(f"成功从 {url} 提取数据: {extracted_data}")
            return extracted_data
            
        except Exception as e:
            self.logger.error(f"访问 {url} 时出错: {str(e)}")
            raise
    
    async def close(self):
        """关闭浏览器"""
        if self.page:
            await self.page.close()
        if self.browser:
            await self.browser.close()
        if self.playwright:
            await self.playwright.stop()
        
        self.logger.info("浏览器已关闭")
```

### 1.3 数据归一化器 (data_normalizer.py)

```python
import re
import json
from datetime import datetime
from typing import Any, Dict, Optional
import logging

class DataNormalizer:
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 数据验证规则
        self.validation_rules = {
            'temperature': {'min': -50, 'max': 60, 'unit': '°C'},
            'humidity': {'min': 0, 'max': 100, 'unit': '%'},
            'pressure': {'min': 800, 'max': 1200, 'unit': 'hPa'}
        }
    
    def normalize(self, raw_data: Dict[str, Any], source_id: str) -> Dict[str, Any]:
        """归一化数据"""
        normalized = {}
        
        for field, value in raw_data.items():
            try:
                # 数据清洗
                cleaned_value = self._clean_value(value)
                
                # 数据转换
                converted_value = self._convert_value(field, cleaned_value)
                
                # 数据验证
                validated_value = self._validate_value(field, converted_value)
                
                normalized[field] = {
                    'value': validated_value,
                    'unit': self.validation_rules.get(field, {}).get('unit', ''),
                    'timestamp': datetime.now().isoformat(),
                    'valid': validated_value is not None
                }
                
            except Exception as e:
                self.logger.error(f"归一化字段 {field} 时出错: {str(e)}")
                normalized[field] = {
                    'value': None,
                    'unit': '',
                    'timestamp': datetime.now().isoformat(),
                    'valid': False,
                    'error': str(e)
                }
        
        return normalized
    
    def _clean_value(self, value: Any) -> str:
        """清洗数据"""
        if value is None:
            return ""
        
        # 转换为字符串并清理
        str_value = str(value).strip()
        
        # 移除多余的空白字符
        str_value = re.sub(r'\s+', ' ', str_value)
        
        # 移除特殊字符（保留数字、小数点、负号）
        if re.match(r'^-?\d+\.?\d*', str_value):
            str_value = re.findall(r'-?\d+\.?\d*', str_value)[0]
        
        return str_value
    
    def _convert_value(self, field: str, value: str) -> Optional[float]:
        """转换数据类型"""
        if not value:
            return None
        
        try:
            # 尝试转换为数字
            if re.match(r'^-?\d+\.?\d*$', value):
                return float(value)
            
            # 特殊字段处理
            if field == 'timestamp':
                return self._parse_timestamp(value)
            
            return value
            
        except ValueError:
            self.logger.warning(f"无法转换值 '{value}' 为数字")
            return None
    
    def _validate_value(self, field: str, value: Any) -> Any:
        """验证数据"""
        if value is None:
            return None
        
        if field in self.validation_rules:
            rules = self.validation_rules[field]
            
            if isinstance(value, (int, float)):
                if value < rules['min'] or value > rules['max']:
                    self.logger.warning(
                        f"字段 {field} 的值 {value} 超出有效范围 "
                        f"[{rules['min']}, {rules['max']}]"
                    )
                    return None
        
        return value
    
    def _parse_timestamp(self, timestamp_str: str) -> str:
        """解析时间戳"""
        try:
            # 尝试多种时间格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y/%m/%d %H:%M:%S',
                '%d/%m/%Y %H:%M:%S',
                '%Y-%m-%d',
                '%d/%m/%Y'
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(timestamp_str, fmt)
                    return dt.isoformat()
                except ValueError:
                    continue
            
            # 如果都不匹配，返回当前时间
            return datetime.now().isoformat()
            
        except Exception:
            return datetime.now().isoformat()
```

### 1.4 RS485通信模块 (rs485_communicator.py)

```python
import serial
import json
import time
import logging
from typing import Dict, Any
from dataclasses import dataclass

@dataclass
class RS485Frame:
    start_byte: int = 0xAA
    end_byte: int = 0x55
    max_data_length: int = 1024

class RS485Communicator:
    def __init__(self, port: str, baudrate: int = 9600, timeout: int = 1):
        self.port = port
        self.baudrate = baudrate
        self.timeout = timeout
        self.serial_conn: serial.Serial = None
        self.logger = logging.getLogger(__name__)
        self.frame_config = RS485Frame()
    
    def connect(self):
        """连接串口"""
        try:
            self.serial_conn = serial.Serial(
                port=self.port,
                baudrate=self.baudrate,
                bytesize=serial.EIGHTBITS,
                parity=serial.PARITY_NONE,
                stopbits=serial.STOPBITS_ONE,
                timeout=self.timeout
            )
            
            self.logger.info(f"成功连接到串口 {self.port}")
            
        except Exception as e:
            self.logger.error(f"连接串口失败: {str(e)}")
            raise
    
    def disconnect(self):
        """断开串口连接"""
        if self.serial_conn and self.serial_conn.is_open:
            self.serial_conn.close()
            self.logger.info("串口连接已断开")
    
    def send_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送数据"""
        try:
            # 构建数据帧
            frame = self._build_frame(data)
            
            # 发送数据
            self.serial_conn.write(frame)
            self.serial_conn.flush()
            
            # 等待响应
            response = self._wait_for_response()
            
            return {
                'success': True,
                'response': response,
                'timestamp': time.time()
            }
            
        except Exception as e:
            self.logger.error(f"发送数据失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': time.time()
            }
    
    def _build_frame(self, data: Dict[str, Any]) -> bytearray:
        """构建RS485数据帧"""
        # 将数据转换为JSON字符串
        json_data = json.dumps(data, ensure_ascii=False).encode('utf-8')
        
        if len(json_data) > self.frame_config.max_data_length:
            raise ValueError(f"数据长度超过最大限制 {self.frame_config.max_data_length}")
        
        # 计算校验和
        checksum = sum(json_data) % 256
        
        # 构建帧
        frame = bytearray()
        frame.append(self.frame_config.start_byte)  # 起始字节
        frame.append(len(json_data) & 0xFF)         # 数据长度（低字节）
        frame.append((len(json_data) >> 8) & 0xFF)  # 数据长度（高字节）
        frame.extend(json_data)                     # 数据内容
        frame.append(checksum)                      # 校验和
        frame.append(self.frame_config.end_byte)    # 结束字节
        
        return frame
    
    def _wait_for_response(self, timeout: int = 5) -> Dict[str, Any]:
        """等待响应"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            if self.serial_conn.in_waiting > 0:
                response_data = self.serial_conn.read(self.serial_conn.in_waiting)
                return self._parse_response(response_data)
            
            time.sleep(0.1)
        
        raise TimeoutError("等待响应超时")
    
    def _parse_response(self, response_data: bytes) -> Dict[str, Any]:
        """解析响应数据"""
        try:
            if len(response_data) < 5:  # 最小帧长度
                raise ValueError("响应数据长度不足")
            
            # 检查起始和结束字节
            if (response_data[0] != self.frame_config.start_byte or 
                response_data[-1] != self.frame_config.end_byte):
                raise ValueError("响应帧格式错误")
            
            # 提取数据长度
            data_length = response_data[1] | (response_data[2] << 8)
            
            # 提取数据内容
            data_content = response_data[3:3+data_length]
            
            # 验证校验和
            expected_checksum = response_data[3+data_length]
            actual_checksum = sum(data_content) % 256
            
            if expected_checksum != actual_checksum:
                raise ValueError("校验和验证失败")
            
            # 解析JSON数据
            json_str = data_content.decode('utf-8')
            return json.loads(json_str)
            
        except Exception as e:
            self.logger.error(f"解析响应数据失败: {str(e)}")
            return {'error': str(e)}
```

## 2. 配置文件示例

### 2.1 主配置文件 (config.yml)
```yaml
system:
  name: "RPG数据采集系统"
  version: "1.0.0"
  debug: false

browser:
  headless: true
  timeout: 30000
  user_agent: "Mozilla/5.0 (compatible; DataCollector/1.0)"
  
serial:
  port: "/dev/ttyUSB0"  # Windows: "COM3"
  baudrate: 9600
  timeout: 1
  
scheduler:
  max_workers: 3
  retry_attempts: 3
  retry_delay: 5
  
logging:
  level: "INFO"
  file: "logs/collector.log"
  max_size: "10MB"
  backup_count: 5
```

### 2.2 数据源配置 (data-sources.yml)
```yaml
sources:
  - id: "weather_station_1"
    name: "气象站1数据"
    url: "https://weather.example.com/station1"
    type: "web_scraping"
    interval: 300  # 5分钟采集一次
    selectors:
      temperature: ".temp-display .value"
      humidity: ".humidity-display .value"
      pressure: ".pressure-display .value"
      wind_speed: ".wind .speed"
    validation:
      temperature: {min: -40, max: 50}
      humidity: {min: 0, max: 100}
      
  - id: "sensor_api_1"
    name: "传感器API数据"
    url: "https://api.sensors.com/v1/data"
    type: "api"
    interval: 600  # 10分钟采集一次
    headers:
      Authorization: "Bearer your-api-token"
      Content-Type: "application/json"
```

## 3. 启动脚本

### 3.1 启动脚本 (start.sh)
```bash
#!/bin/bash

# 设置环境变量
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# 创建日志目录
mkdir -p logs

# 检查Python版本
python_version=$(python3 --version 2>&1 | awk '{print $2}')
echo "Python版本: $python_version"

# 检查依赖
echo "检查依赖包..."
pip3 install -r requirements.txt

# 检查串口设备
if [ -e "/dev/ttyUSB0" ]; then
    echo "检测到串口设备: /dev/ttyUSB0"
else
    echo "警告: 未检测到串口设备 /dev/ttyUSB0"
fi

# 启动系统
echo "启动RPG数据采集系统..."
python3 main.py
```

### 3.2 依赖文件 (requirements.txt)
```
playwright==1.40.0
pyserial==3.5
pyyaml==6.0.1
asyncio==3.4.3
dataclasses==0.8
```

这个技术实现指南提供了完整的代码框架，您可以根据具体需求进行调整和扩展。
