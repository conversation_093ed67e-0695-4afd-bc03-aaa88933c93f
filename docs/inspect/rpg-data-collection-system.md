# RPG数据采集与转发系统设计方案

## 1. 系统概述

本方案设计了一个基于RPG（Robotic Process Automation）的数据采集系统，通过浏览器自动化采集数据，经过数据归一化处理后，通过RS485串口转发到服务器进行后续处理。

### 1.1 核心特性
- 🤖 基于RPG的自动化数据采集
- 🌐 支持多种Web数据源
- 📊 数据归一化处理
- 🔌 RS485串口通信
- 📈 实时数据监控
- 🛡️ 异常处理与重试机制

## 2. 系统架构

### 2.1 整体架构图

```mermaid
graph TB
    subgraph "数据源层"
        A1[Web页面1]
        A2[Web页面2]
        A3[Web页面N]
    end
    
    subgraph "RPG采集层"
        B1[浏览器控制器]
        B2[数据提取器]
        B3[任务调度器]
    end
    
    subgraph "数据处理层"
        C1[数据归一化]
        C2[数据验证]
        C3[格式转换]
    end
    
    subgraph "通信层"
        D1[RS485接口]
        D2[协议转换]
        D3[数据缓存]
    end
    
    subgraph "服务器层"
        E1[数据接收服务]
        E2[业务处理]
        E3[数据存储]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> B2
    B2 --> B3
    B3 --> C1
    
    C1 --> C2
    C2 --> C3
    C3 --> D1
    
    D1 --> D2
    D2 --> D3
    D3 --> E1
    
    E1 --> E2
    E2 --> E3
```

### 2.2 技术栈选择

| 组件 | 技术选型 | 说明 |
|------|----------|------|
| RPG引擎 | Playwright/Selenium | 浏览器自动化控制 |
| 数据处理 | Python/Node.js | 数据归一化和转换 |
| 串口通信 | pySerial/serialport | RS485通信库 |
| 任务调度 | Celery/Bull | 异步任务处理 |
| 数据缓存 | Redis | 临时数据存储 |
| 监控日志 | Winston/Loguru | 系统监控和日志 |

## 3. 详细设计

### 3.1 RPG数据采集模块

#### 3.1.1 浏览器控制器
```javascript
class BrowserController {
    constructor(config) {
        this.browser = null;
        this.page = null;
        this.config = config;
    }
    
    async initialize() {
        this.browser = await playwright.chromium.launch({
            headless: this.config.headless,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        this.page = await this.browser.newPage();
    }
    
    async navigateAndExtract(url, selectors) {
        await this.page.goto(url);
        await this.page.waitForLoadState('networkidle');
        
        const data = {};
        for (const [key, selector] of Object.entries(selectors)) {
            data[key] = await this.page.textContent(selector);
        }
        return data;
    }
}
```

#### 3.1.2 数据提取配置
```yaml
# extraction-config.yml
sites:
  - name: "数据源1"
    url: "https://example1.com/data"
    interval: 300  # 5分钟
    selectors:
      temperature: ".temp-value"
      humidity: ".humidity-value"
      timestamp: ".update-time"
    
  - name: "数据源2"
    url: "https://example2.com/api"
    interval: 600  # 10分钟
    type: "api"
    headers:
      Authorization: "Bearer token"
```

### 3.2 数据归一化处理

#### 3.2.1 数据标准化流程
```python
class DataNormalizer:
    def __init__(self):
        self.validators = {
            'temperature': self._validate_temperature,
            'humidity': self._validate_humidity,
            'timestamp': self._validate_timestamp
        }
    
    def normalize(self, raw_data, source_config):
        normalized = {}
        
        for field, value in raw_data.items():
            # 数据清洗
            cleaned_value = self._clean_value(value)
            
            # 数据验证
            if field in self.validators:
                validated_value = self.validators[field](cleaned_value)
            else:
                validated_value = cleaned_value
            
            # 格式转换
            normalized[field] = self._convert_format(
                validated_value, 
                source_config.get('formats', {}).get(field)
            )
        
        return normalized
    
    def _clean_value(self, value):
        if isinstance(value, str):
            return value.strip().replace('\n', '').replace('\t', '')
        return value
```

### 3.3 RS485通信模块

#### 3.3.1 串口通信协议
```python
class RS485Communicator:
    def __init__(self, port='/dev/ttyUSB0', baudrate=9600):
        self.serial_conn = serial.Serial(
            port=port,
            baudrate=baudrate,
            bytesize=serial.EIGHTBITS,
            parity=serial.PARITY_NONE,
            stopbits=serial.STOPBITS_ONE,
            timeout=1
        )
    
    def send_data(self, data):
        # 数据帧格式: [起始位][长度][数据][校验位][结束位]
        frame = self._build_frame(data)
        
        try:
            self.serial_conn.write(frame)
            response = self.serial_conn.read(1024)
            return self._parse_response(response)
        except Exception as e:
            logger.error(f"串口通信错误: {e}")
            raise
    
    def _build_frame(self, data):
        # 构建数据帧
        json_data = json.dumps(data).encode('utf-8')
        length = len(json_data)
        checksum = sum(json_data) % 256
        
        frame = bytearray()
        frame.append(0xAA)  # 起始位
        frame.append(length)  # 数据长度
        frame.extend(json_data)  # 数据内容
        frame.append(checksum)  # 校验位
        frame.append(0x55)  # 结束位
        
        return frame
```

## 4. 系统流程

### 4.1 数据采集流程图

```mermaid
flowchart TD
    A[系统启动] --> B[加载配置文件]
    B --> C[初始化浏览器]
    C --> D[启动任务调度器]
    
    D --> E{检查采集任务}
    E -->|有任务| F[执行网页访问]
    E -->|无任务| G[等待下次检查]
    
    F --> H[提取页面数据]
    H --> I[数据预处理]
    I --> J[数据归一化]
    J --> K[数据验证]
    
    K -->|验证通过| L[构建数据帧]
    K -->|验证失败| M[记录错误日志]
    
    L --> N[RS485发送]
    N --> O{发送成功?}
    
    O -->|成功| P[更新状态]
    O -->|失败| Q[重试机制]
    
    Q --> R{重试次数<3?}
    R -->|是| N
    R -->|否| S[标记失败]
    
    P --> G
    S --> G
    M --> G
    G --> E
```

### 4.2 错误处理流程

```mermaid
flowchart TD
    A[检测到异常] --> B{异常类型}
    
    B -->|网络异常| C[等待网络恢复]
    B -->|页面加载失败| D[重新加载页面]
    B -->|数据提取失败| E[使用备用选择器]
    B -->|串口通信失败| F[重新连接串口]
    B -->|数据验证失败| G[记录异常数据]
    
    C --> H[重试操作]
    D --> H
    E --> H
    F --> H
    G --> I[继续下一任务]
    
    H --> J{重试成功?}
    J -->|是| K[恢复正常流程]
    J -->|否| L{达到最大重试次数?}
    
    L -->|是| M[标记任务失败]
    L -->|否| H
    
    M --> N[发送告警通知]
    N --> I
    K --> O[继续正常流程]
```

## 5. 配置管理

### 5.1 系统配置文件
```yaml
# config.yml
system:
  name: "RPG数据采集系统"
  version: "1.0.0"
  
browser:
  headless: true
  timeout: 30000
  user_agent: "Mozilla/5.0 (compatible; DataCollector/1.0)"
  
serial:
  port: "/dev/ttyUSB0"
  baudrate: 9600
  timeout: 1
  
scheduler:
  max_workers: 5
  retry_attempts: 3
  retry_delay: 5
  
logging:
  level: "INFO"
  file: "logs/collector.log"
  max_size: "10MB"
  backup_count: 5
```

### 5.2 数据源配置
```yaml
# data-sources.yml
sources:
  - id: "weather_station_1"
    name: "气象站1"
    url: "https://weather.example.com/station1"
    type: "web_scraping"
    interval: 300
    selectors:
      temperature: ".temperature .value"
      humidity: ".humidity .value"
      pressure: ".pressure .value"
    validation:
      temperature: 
        min: -50
        max: 60
      humidity:
        min: 0
        max: 100
```

## 6. 部署和运维

### 6.1 系统要求
- **操作系统**: Linux/Windows/macOS
- **Python版本**: 3.8+
- **内存**: 最低2GB，推荐4GB
- **存储**: 最低10GB可用空间
- **网络**: 稳定的互联网连接
- **硬件**: RS485转USB适配器

### 6.2 安装部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd rpg-data-collector

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置系统
cp config.example.yml config.yml
# 编辑配置文件

# 4. 启动服务
python main.py
```

### 6.3 监控指标
- 数据采集成功率
- 串口通信延迟
- 系统资源使用率
- 错误日志统计
- 任务执行时间

## 7. 扩展性设计

### 7.1 插件化架构
系统支持通过插件扩展新的数据源类型和处理方式：

```python
class DataSourcePlugin:
    def __init__(self, config):
        self.config = config
    
    def extract_data(self):
        raise NotImplementedError
    
    def validate_data(self, data):
        raise NotImplementedError
```

### 7.2 未来扩展方向
- 支持更多通信协议（Modbus、CAN总线等）
- 增加机器学习数据预处理
- 支持分布式部署
- 增加Web管理界面
- 支持实时数据流处理

## 8. 总结

本方案提供了一个完整的RPG数据采集与转发系统，具备高可靠性、可扩展性和易维护性。通过模块化设计，系统可以灵活适应不同的数据源和业务需求，为后续的数据处理和分析提供稳定的数据基础。
