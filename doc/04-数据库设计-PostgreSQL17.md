# 04-数据库设计-PostgreSQL17

## 1. PostgreSQL 17 选择理由

### 1.1 技术优势
- **企业级特性**: 支持ACID事务、并发控制、数据完整性
- **多租户支持**: 原生支持行级安全(RLS)、Schema级隔离
- **JSON支持**: 原生JSON/JSONB数据类型，适合存储动态配置
- **全文搜索**: 内置全文搜索功能，无需额外搜索引擎
- **扩展性**: 丰富的扩展生态，如UUID、加密、时间序列等
- **安全性**: 强大的权限管理、SSL支持、数据加密

### 1.2 认证授权系统特定优势
- **行级安全(RLS)**: 完美支持多租户数据隔离
- **角色权限**: 数据库级别的角色权限管理
- **审计日志**: 内置审计功能，记录所有数据变更
- **并发性能**: 优秀的读写并发性能，适合高频认证场景
- **JSON权限**: 使用JSONB存储复杂权限结构

## 2. 数据库架构设计

### 2.1 多租户架构策略

#### 策略1: 共享数据库 + 行级安全(RLS)
```sql
-- 启用行级安全
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

-- 创建租户隔离策略
CREATE POLICY tenant_isolation ON users
    FOR ALL
    TO application_role
    USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
```

#### 策略2: Schema级隔离(大租户)
```sql
-- 为大租户创建独立Schema
CREATE SCHEMA tenant_enterprise_001;
CREATE TABLE tenant_enterprise_001.users (LIKE public.users INCLUDING ALL);
```

### 2.2 数据库连接架构
```mermaid
graph TB
    subgraph "应用层"
        APP1[认证服务]
        APP2[用户服务]
        APP3[租户服务]
    end

    subgraph "连接池层"
        POOL1[HikariCP主连接池]
        POOL2[HikariCP只读连接池]
        POOL3[租户专用连接池]
    end

    subgraph "数据库层"
        PG_MASTER[(PostgreSQL 17 主库)]
        PG_SLAVE1[(PostgreSQL 17 从库1)]
        PG_SLAVE2[(PostgreSQL 17 从库2)]
    end

    APP1 --> POOL1
    APP2 --> POOL1
    APP3 --> POOL1
    
    APP1 --> POOL2
    APP2 --> POOL2
    
    POOL1 --> PG_MASTER
    POOL2 --> PG_SLAVE1
    POOL2 --> PG_SLAVE2
    POOL3 --> PG_MASTER
    
    PG_MASTER --> PG_SLAVE1
    PG_MASTER --> PG_SLAVE2
```

## 3. 核心表结构设计

### 3.1 用户表 (users) - 全局用户，支持两级认证体系
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    phone VARCHAR(20) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(100),
    avatar_url TEXT,
    status user_status_enum DEFAULT 'pending',
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(255),
    mfa_backup_codes JSONB, -- 存储备用恢复码
    preferences JSONB DEFAULT '{}', -- 用户偏好设置
    metadata JSONB DEFAULT '{}', -- 扩展元数据
    last_login_at TIMESTAMPTZ,
    last_login_ip INET,
    failed_login_attempts INTEGER DEFAULT 0,
    locked_until TIMESTAMPTZ,
    password_changed_at TIMESTAMPTZ DEFAULT NOW(),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- 约束
    CONSTRAINT users_email_format CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT users_phone_format CHECK (phone ~ '^\+?[1-9]\d{1,14}$')
);

-- 创建枚举类型
CREATE TYPE user_status_enum AS ENUM ('pending', 'active', 'locked', 'disabled', 'deleted');

-- 索引
CREATE INDEX idx_users_email ON users(email) WHERE email IS NOT NULL;
CREATE INDEX idx_users_phone ON users(phone) WHERE phone IS NOT NULL;
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_last_login ON users(last_login_at);
CREATE INDEX idx_users_username ON users(username);

-- 注意：用户表不启用行级安全，因为用户是全局的
-- 租户级的数据隔离通过user_tenants表实现

-- 触发器：自动更新updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 3.2 租户表 (tenants)
```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    domain VARCHAR(255) UNIQUE,
    description TEXT,
    logo_url TEXT,
    status tenant_status_enum DEFAULT 'active',
    tier tenant_tier_enum DEFAULT 'standard',
    settings JSONB DEFAULT '{}', -- 租户配置
    security_policy JSONB DEFAULT '{}', -- 安全策略
    branding JSONB DEFAULT '{}', -- 品牌定制
    limits JSONB DEFAULT '{}', -- 使用限制
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 约束
    CONSTRAINT tenants_domain_format CHECK (domain ~* '^[a-z0-9]([a-z0-9-]*[a-z0-9])?(\.[a-z0-9]([a-z0-9-]*[a-z0-9])?)*$')
);

-- 创建枚举类型
CREATE TYPE tenant_status_enum AS ENUM ('active', 'suspended', 'deleted');
CREATE TYPE tenant_tier_enum AS ENUM ('trial', 'standard', 'premium', 'enterprise');

-- 索引
CREATE UNIQUE INDEX idx_tenants_domain ON tenants(domain) WHERE domain IS NOT NULL;
CREATE INDEX idx_tenants_status ON tenants(status);
CREATE INDEX idx_tenants_tier ON tenants(tier);

-- 触发器
CREATE TRIGGER update_tenants_updated_at 
    BEFORE UPDATE ON tenants 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 3.3 用户租户关系表 (user_tenants) - 两级认证体系的核心关系表
```sql
CREATE TABLE user_tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL,
    status user_tenant_status_enum DEFAULT 'pending',
    permissions JSONB DEFAULT '{}', -- 用户在租户中的特殊权限
    invited_by UUID REFERENCES users(id),
    invited_at TIMESTAMPTZ,
    joined_at TIMESTAMPTZ,
    last_accessed_at TIMESTAMPTZ,
    is_default BOOLEAN DEFAULT FALSE, -- 用户的默认租户
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    -- 约束
    UNIQUE(user_id, tenant_id),
    -- 确保每个用户只有一个默认租户
    EXCLUDE (user_id WITH =) WHERE (is_default = true)
);

-- 创建枚举类型
CREATE TYPE user_tenant_status_enum AS ENUM ('pending', 'active', 'inactive', 'suspended');

-- 索引
CREATE INDEX idx_user_tenants_user_id ON user_tenants(user_id);
CREATE INDEX idx_user_tenants_tenant_id ON user_tenants(tenant_id);
CREATE INDEX idx_user_tenants_status ON user_tenants(status);
CREATE INDEX idx_user_tenants_role ON user_tenants(role);
CREATE INDEX idx_user_tenants_user_status ON user_tenants(user_id, status);
CREATE INDEX idx_user_tenants_default ON user_tenants(user_id, is_default) WHERE is_default = true;

-- 行级安全策略 - 基于租户隔离
ALTER TABLE user_tenants ENABLE ROW LEVEL SECURITY;
CREATE POLICY user_tenants_tenant_isolation ON user_tenants
    FOR ALL TO application_role
    USING (
        tenant_id = current_setting('app.current_tenant_id')::uuid OR
        user_id = current_setting('app.current_user_id')::uuid
    );

-- 触发器
CREATE TRIGGER update_user_tenants_updated_at
    BEFORE UPDATE ON user_tenants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自动设置joined_at时间
CREATE OR REPLACE FUNCTION set_joined_at()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status != 'active' AND NEW.status = 'active' AND NEW.joined_at IS NULL THEN
        NEW.joined_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER set_user_tenant_joined_at
    BEFORE UPDATE ON user_tenants
    FOR EACH ROW EXECUTE FUNCTION set_joined_at();
```

### 3.4 邀请表 (invitations)
```sql
CREATE TABLE invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    inviter_id UUID NOT NULL REFERENCES users(id),
    email VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL,
    message TEXT,
    status invitation_status_enum DEFAULT 'pending',
    token VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES users(id), -- 接受邀请的用户ID
    permissions JSONB DEFAULT '{}', -- 邀请的权限配置
    expires_at TIMESTAMPTZ NOT NULL,
    accepted_at TIMESTAMPTZ,
    rejected_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建枚举类型
CREATE TYPE invitation_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'expired', 'cancelled');

-- 索引
CREATE INDEX idx_invitations_email_status ON invitations(email, status);
CREATE INDEX idx_invitations_tenant_status ON invitations(tenant_id, status);
CREATE INDEX idx_invitations_token ON invitations(token);
CREATE INDEX idx_invitations_expires_at ON invitations(expires_at);

-- 触发器
CREATE TRIGGER update_invitations_updated_at 
    BEFORE UPDATE ON invitations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 自动过期邀请的函数
CREATE OR REPLACE FUNCTION expire_invitations()
RETURNS void AS $$
BEGIN
    UPDATE invitations 
    SET status = 'expired', updated_at = NOW()
    WHERE status = 'pending' AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;
```

### 3.5 申请表 (applications)
```sql
CREATE TABLE applications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    message TEXT,
    expected_role VARCHAR(50) NOT NULL,
    status application_status_enum DEFAULT 'pending',
    processed_by UUID REFERENCES users(id),
    processed_at TIMESTAMPTZ,
    process_message TEXT,
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 约束：同一用户对同一租户只能有一个待处理申请
    UNIQUE(user_id, tenant_id) WHERE status = 'pending'
);

-- 创建枚举类型
CREATE TYPE application_status_enum AS ENUM ('pending', 'approved', 'rejected', 'expired', 'cancelled');

-- 索引
CREATE INDEX idx_applications_tenant_status ON applications(tenant_id, status);
CREATE INDEX idx_applications_user_id ON applications(user_id);
CREATE INDEX idx_applications_expires_at ON applications(expires_at);

-- 触发器
CREATE TRIGGER update_applications_updated_at 
    BEFORE UPDATE ON applications 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 4. 权限管理表设计

### 4.1 角色表 (roles)
```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    permissions JSONB NOT NULL DEFAULT '{}',
    is_system BOOLEAN DEFAULT FALSE,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 约束：租户内角色名唯一
    UNIQUE(tenant_id, name)
);

-- 索引
CREATE INDEX idx_roles_tenant_id ON roles(tenant_id);
CREATE INDEX idx_roles_is_system ON roles(is_system);
CREATE INDEX idx_roles_is_default ON roles(is_default);

-- 触发器
CREATE TRIGGER update_roles_updated_at 
    BEFORE UPDATE ON roles 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 4.2 权限资源表 (resources)
```sql
CREATE TABLE resources (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    type resource_type_enum NOT NULL,
    path VARCHAR(255),
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- 约束：租户内资源路径唯一
    UNIQUE(tenant_id, path)
);

-- 创建枚举类型
CREATE TYPE resource_type_enum AS ENUM ('api', 'page', 'button', 'data', 'file');

-- 索引
CREATE INDEX idx_resources_tenant_id ON resources(tenant_id);
CREATE INDEX idx_resources_type ON resources(type);
CREATE INDEX idx_resources_path ON resources(path);

-- 触发器
CREATE TRIGGER update_resources_updated_at 
    BEFORE UPDATE ON resources 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 5. 审计和日志表设计

### 5.1 审计日志表 (audit_logs) - 采用继承表按月分表
```sql
-- 审计日志主表（父表）
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID,
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id UUID,
    details JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    request_id VARCHAR(255),
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_month DATE GENERATED ALWAYS AS (date_trunc('month', timestamp)::date) STORED
);

-- 创建索引模板（会被子表继承）
CREATE INDEX idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_session ON audit_logs(session_id) WHERE session_id IS NOT NULL;

-- 创建当月审计日志表的函数
CREATE OR REPLACE FUNCTION create_audit_logs_table(target_date DATE)
RETURNS TEXT AS $$
DECLARE
    table_name TEXT;
    start_date DATE;
    end_date DATE;
    sql_text TEXT;
BEGIN
    -- 计算表名和日期范围
    table_name := 'audit_logs_' || to_char(target_date, 'YYYY_MM');
    start_date := date_trunc('month', target_date)::date;
    end_date := (date_trunc('month', target_date) + interval '1 month')::date;

    -- 检查表是否已存在
    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_name = table_name AND table_schema = 'public') THEN
        RETURN 'Table ' || table_name || ' already exists';
    END IF;

    -- 创建继承表
    sql_text := format('
        CREATE TABLE %I (
            CHECK (timestamp >= %L AND timestamp < %L)
        ) INHERITS (audit_logs)',
        table_name, start_date, end_date
    );

    EXECUTE sql_text;

    -- 创建表特定的索引
    EXECUTE format('CREATE INDEX idx_%I_timestamp ON %I(timestamp)', table_name, table_name);
    EXECUTE format('CREATE INDEX idx_%I_tenant_timestamp ON %I(tenant_id, timestamp)', table_name, table_name);
    EXECUTE format('CREATE INDEX idx_%I_user_timestamp ON %I(user_id, timestamp)', table_name, table_name);
    EXECUTE format('CREATE INDEX idx_%I_action_timestamp ON %I(action, timestamp)', table_name, table_name);

    -- 创建触发器函数用于自动插入到正确的子表
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'audit_logs_insert_trigger') THEN
        EXECUTE '
        CREATE OR REPLACE FUNCTION audit_logs_insert_trigger()
        RETURNS TRIGGER AS $trigger$
        DECLARE
            table_name TEXT;
        BEGIN
            table_name := ''audit_logs_'' || to_char(NEW.timestamp, ''YYYY_MM'');

            -- 动态插入到对应的月表
            EXECUTE format(''INSERT INTO %I VALUES ($1.*)'', table_name) USING NEW;

            RETURN NULL; -- 阻止插入到父表
        EXCEPTION
            WHEN undefined_table THEN
                -- 如果表不存在，先创建表再插入
                PERFORM create_audit_logs_table(NEW.timestamp::date);
                EXECUTE format(''INSERT INTO %I VALUES ($1.*)'', table_name) USING NEW;
                RETURN NULL;
        END;
        $trigger$ LANGUAGE plpgsql;
        ';

        -- 创建触发器
        EXECUTE '
        CREATE TRIGGER audit_logs_insert_trigger
            BEFORE INSERT ON audit_logs
            FOR EACH ROW EXECUTE FUNCTION audit_logs_insert_trigger();
        ';
    END IF;

    RETURN 'Created table ' || table_name;
END;
$$ LANGUAGE plpgsql;

-- 初始化当月和下月的审计日志表
SELECT create_audit_logs_table(CURRENT_DATE);
SELECT create_audit_logs_table(CURRENT_DATE + interval '1 month');

-- 创建自动创建下月表的函数
CREATE OR REPLACE FUNCTION create_next_month_audit_table()
RETURNS void AS $$
DECLARE
    next_month_date DATE;
    result TEXT;
BEGIN
    next_month_date := (date_trunc('month', CURRENT_DATE) + interval '1 months')::date;
    SELECT create_audit_logs_table(next_month_date) INTO result;

    -- 记录日志
    INSERT INTO audit_logs (action, resource_type, details, timestamp)
    VALUES (
        'system.table_created',
        'audit_logs',
        jsonb_build_object('result', result, 'date', next_month_date),
        NOW()
    );
END;
$$ LANGUAGE plpgsql;

-- 创建定时任务，每月1号自动创建下月的表
-- 注意：需要安装pg_cron扩展
-- SELECT cron.schedule('create-audit-table', '0 0 1 * *', 'SELECT create_next_month_audit_table();');

-- 创建查询所有审计日志的视图
CREATE VIEW audit_logs_all AS
SELECT * FROM audit_logs
UNION ALL
SELECT * FROM audit_logs_2024_01  -- 这里需要根据实际存在的表动态生成
-- 可以通过函数动态生成UNION ALL语句

-- 创建清理旧审计日志的函数（保留指定月数）
CREATE OR REPLACE FUNCTION cleanup_old_audit_logs(retain_months INTEGER DEFAULT 24)
RETURNS TEXT AS $$
DECLARE
    cutoff_date DATE;
    table_record RECORD;
    table_name TEXT;
    result_text TEXT := '';
BEGIN
    cutoff_date := (date_trunc('month', CURRENT_DATE) - (retain_months || ' months')::interval)::date;

    -- 查找需要删除的表
    FOR table_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'audit_logs_%'
        AND tablename ~ '^audit_logs_[0-9]{4}_[0-9]{2}$'
        AND to_date(substring(tablename from 12), 'YYYY_MM') < cutoff_date
    LOOP
        table_name := table_record.tablename;
        EXECUTE format('DROP TABLE IF EXISTS %I CASCADE', table_name);
        result_text := result_text || 'Dropped table ' || table_name || '; ';
    END LOOP;

    IF result_text = '' THEN
        result_text := 'No tables to cleanup';
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- 创建获取审计日志表统计信息的函数
CREATE OR REPLACE FUNCTION get_audit_logs_stats()
RETURNS TABLE(
    table_name TEXT,
    month_year TEXT,
    record_count BIGINT,
    table_size TEXT,
    index_size TEXT
) AS $$
DECLARE
    table_record RECORD;
BEGIN
    FOR table_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'audit_logs_%'
        AND tablename ~ '^audit_logs_[0-9]{4}_[0-9]{2}$'
        ORDER BY tablename
    LOOP
        table_name := table_record.tablename;
        month_year := substring(table_record.tablename from 12);

        EXECUTE format('SELECT COUNT(*) FROM %I', table_record.tablename) INTO record_count;

        SELECT pg_size_pretty(pg_total_relation_size(table_record.tablename::regclass)) INTO table_size;
        SELECT pg_size_pretty(pg_indexes_size(table_record.tablename::regclass)) INTO index_size;

        RETURN NEXT;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 创建查询指定时间范围审计日志的函数
CREATE OR REPLACE FUNCTION query_audit_logs(
    start_date TIMESTAMPTZ,
    end_date TIMESTAMPTZ,
    p_tenant_id UUID DEFAULT NULL,
    p_user_id UUID DEFAULT NULL,
    p_action VARCHAR DEFAULT NULL,
    limit_count INTEGER DEFAULT 1000
)
RETURNS TABLE(
    id UUID,
    tenant_id UUID,
    user_id UUID,
    action VARCHAR,
    resource_type VARCHAR,
    resource_id UUID,
    details JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR,
    request_id VARCHAR,
    timestamp TIMESTAMPTZ
) AS $$
DECLARE
    table_record RECORD;
    sql_query TEXT;
    where_clause TEXT := '';
    union_queries TEXT := '';
BEGIN
    -- 构建WHERE条件
    where_clause := format('timestamp >= %L AND timestamp <= %L', start_date, end_date);

    IF p_tenant_id IS NOT NULL THEN
        where_clause := where_clause || format(' AND tenant_id = %L', p_tenant_id);
    END IF;

    IF p_user_id IS NOT NULL THEN
        where_clause := where_clause || format(' AND user_id = %L', p_user_id);
    END IF;

    IF p_action IS NOT NULL THEN
        where_clause := where_clause || format(' AND action = %L', p_action);
    END IF;

    -- 查找涉及的表
    FOR table_record IN
        SELECT tablename
        FROM pg_tables
        WHERE tablename LIKE 'audit_logs_%'
        AND tablename ~ '^audit_logs_[0-9]{4}_[0-9]{2}$'
        ORDER BY tablename
    LOOP
        IF union_queries != '' THEN
            union_queries := union_queries || ' UNION ALL ';
        END IF;

        union_queries := union_queries || format(
            'SELECT * FROM %I WHERE %s',
            table_record.tablename,
            where_clause
        );
    END LOOP;

    -- 执行查询
    IF union_queries != '' THEN
        sql_query := format(
            'SELECT * FROM (%s) AS combined_logs ORDER BY timestamp DESC LIMIT %s',
            union_queries,
            limit_count
        );

        RETURN QUERY EXECUTE sql_query;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 使用示例
/*
-- 插入审计日志（会自动路由到正确的月表）
INSERT INTO audit_logs (tenant_id, user_id, action, resource_type, resource_id, details, ip_address, user_agent, session_id)
VALUES (
    'tenant-uuid',
    'user-uuid',
    'user.login',
    'user',
    'user-uuid',
    '{"success": true, "mfa_used": false}',
    '*************',
    'Mozilla/5.0...',
    'session-uuid'
);

-- 查询最近7天的审计日志
SELECT * FROM query_audit_logs(
    NOW() - interval '7 days',
    NOW(),
    NULL, -- 所有租户
    NULL, -- 所有用户
    NULL, -- 所有操作
    100   -- 限制100条
);

-- 查询特定用户的登录日志
SELECT * FROM query_audit_logs(
    NOW() - interval '30 days',
    NOW(),
    NULL,
    'user-uuid',
    'user.login',
    50
);

-- 获取审计日志表统计信息
SELECT * FROM get_audit_logs_stats();

-- 清理2年前的审计日志
SELECT cleanup_old_audit_logs(24);

-- 手动创建指定月份的审计日志表
SELECT create_audit_logs_table('2024-12-01'::date);
*/
```

## 6. 数据库优化配置

### 6.1 PostgreSQL 17 配置优化
```ini
# postgresql.conf 关键配置
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200

# 连接配置
max_connections = 200
shared_preload_libraries = 'pg_stat_statements'

# 日志配置
log_statement = 'mod'
log_min_duration_statement = 1000
log_checkpoints = on
log_connections = on
log_disconnections = on
```

### 6.2 性能监控查询
```sql
-- 查看慢查询
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
ORDER BY mean_time DESC
LIMIT 10;

-- 查看表大小
SELECT schemaname, tablename, 
       pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- 查看索引使用情况
SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch
FROM pg_stat_user_indexes
ORDER BY idx_scan DESC;
```

## 7. OAuth 2.0 相关表设计

### 7.1 OAuth客户端表 (oauth_clients)
```sql
CREATE TABLE oauth_clients (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    client_id VARCHAR(255) UNIQUE NOT NULL,
    client_secret VARCHAR(255),
    client_name VARCHAR(255) NOT NULL,
    client_description TEXT,
    client_type oauth_client_type_enum DEFAULT 'confidential',
    grant_types TEXT[] NOT NULL DEFAULT ARRAY['authorization_code'],
    redirect_uris TEXT[] NOT NULL DEFAULT ARRAY[]::TEXT[],
    scopes TEXT[] NOT NULL DEFAULT ARRAY['openid', 'profile'],
    access_token_validity INTEGER DEFAULT 3600, -- 秒
    refresh_token_validity INTEGER DEFAULT 604800, -- 7天
    auto_approve BOOLEAN DEFAULT FALSE,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- 创建枚举类型
CREATE TYPE oauth_client_type_enum AS ENUM ('public', 'confidential');

-- 索引
CREATE INDEX idx_oauth_clients_tenant_id ON oauth_clients(tenant_id);
CREATE INDEX idx_oauth_clients_client_id ON oauth_clients(client_id);

-- 触发器
CREATE TRIGGER update_oauth_clients_updated_at
    BEFORE UPDATE ON oauth_clients
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 7.2 OAuth授权码表 (oauth_authorization_codes)
```sql
CREATE TABLE oauth_authorization_codes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(255) UNIQUE NOT NULL,
    client_id VARCHAR(255) NOT NULL REFERENCES oauth_clients(client_id),
    user_id UUID NOT NULL REFERENCES users(id),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    redirect_uri TEXT NOT NULL,
    scopes TEXT[] NOT NULL,
    code_challenge VARCHAR(255), -- PKCE
    code_challenge_method VARCHAR(10), -- S256 or plain
    expires_at TIMESTAMPTZ NOT NULL,
    used_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_oauth_codes_code ON oauth_authorization_codes(code);
CREATE INDEX idx_oauth_codes_client_user ON oauth_authorization_codes(client_id, user_id);
CREATE INDEX idx_oauth_codes_expires_at ON oauth_authorization_codes(expires_at);

-- 自动清理过期授权码
CREATE OR REPLACE FUNCTION cleanup_expired_codes()
RETURNS void AS $$
BEGIN
    DELETE FROM oauth_authorization_codes WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;
```

### 7.3 OAuth访问令牌表 (oauth_access_tokens)
```sql
CREATE TABLE oauth_access_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_id VARCHAR(255) UNIQUE NOT NULL,
    token_hash VARCHAR(255) NOT NULL, -- SHA256哈希
    client_id VARCHAR(255) NOT NULL REFERENCES oauth_clients(client_id),
    user_id UUID NOT NULL REFERENCES users(id),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    scopes TEXT[] NOT NULL,
    expires_at TIMESTAMPTZ NOT NULL,
    revoked_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_oauth_tokens_token_id ON oauth_access_tokens(token_id);
CREATE INDEX idx_oauth_tokens_client_user ON oauth_access_tokens(client_id, user_id);
CREATE INDEX idx_oauth_tokens_expires_at ON oauth_access_tokens(expires_at);
CREATE INDEX idx_oauth_tokens_revoked ON oauth_access_tokens(revoked_at) WHERE revoked_at IS NULL;
```

### 7.4 OAuth刷新令牌表 (oauth_refresh_tokens)
```sql
CREATE TABLE oauth_refresh_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token_id VARCHAR(255) UNIQUE NOT NULL,
    token_hash VARCHAR(255) NOT NULL,
    access_token_id UUID NOT NULL REFERENCES oauth_access_tokens(id) ON DELETE CASCADE,
    expires_at TIMESTAMPTZ NOT NULL,
    revoked_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_oauth_refresh_tokens_token_id ON oauth_refresh_tokens(token_id);
CREATE INDEX idx_oauth_refresh_tokens_access_token ON oauth_refresh_tokens(access_token_id);
CREATE INDEX idx_oauth_refresh_tokens_expires_at ON oauth_refresh_tokens(expires_at);
```

## 8. 会话管理表设计

### 8.1 用户会话表 (user_sessions)
```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    ip_address INET NOT NULL,
    user_agent TEXT,
    device_info JSONB DEFAULT '{}',
    location_info JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT TRUE,
    last_accessed_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- 索引
CREATE INDEX idx_user_sessions_session_id ON user_sessions(session_id);
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active) WHERE is_active = TRUE;
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
```

### 8.2 设备信任表 (trusted_devices)
```sql
CREATE TABLE trusted_devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    device_fingerprint VARCHAR(255) NOT NULL,
    device_name VARCHAR(255),
    device_type VARCHAR(50), -- mobile, desktop, tablet
    browser_info JSONB DEFAULT '{}',
    trusted_at TIMESTAMPTZ DEFAULT NOW(),
    last_used_at TIMESTAMPTZ DEFAULT NOW(),
    expires_at TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id, device_fingerprint)
);

-- 索引
CREATE INDEX idx_trusted_devices_user_id ON trusted_devices(user_id);
CREATE INDEX idx_trusted_devices_fingerprint ON trusted_devices(device_fingerprint);
CREATE INDEX idx_trusted_devices_active ON trusted_devices(is_active) WHERE is_active = TRUE;
```

## 9. 通知和消息表设计

### 9.1 通知模板表 (notification_templates)
```sql
CREATE TABLE notification_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    type notification_type_enum NOT NULL,
    subject VARCHAR(255),
    content TEXT NOT NULL,
    variables JSONB DEFAULT '{}', -- 模板变量定义
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(tenant_id, name, type)
);

-- 创建枚举类型
CREATE TYPE notification_type_enum AS ENUM ('email', 'sms', 'push', 'in_app');

-- 索引
CREATE INDEX idx_notification_templates_tenant_type ON notification_templates(tenant_id, type);
CREATE INDEX idx_notification_templates_active ON notification_templates(is_active) WHERE is_active = TRUE;

-- 触发器
CREATE TRIGGER update_notification_templates_updated_at
    BEFORE UPDATE ON notification_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### 9.2 通知记录表 (notifications) - 采用继承表按月分表
```sql
-- 创建枚举类型
CREATE TYPE notification_status_enum AS ENUM ('pending', 'sent', 'delivered', 'failed', 'read');

-- 通知记录主表（父表）
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    template_id UUID REFERENCES notification_templates(id),
    type notification_type_enum NOT NULL,
    recipient VARCHAR(255) NOT NULL, -- 邮箱、手机号等
    subject VARCHAR(255),
    content TEXT NOT NULL,
    status notification_status_enum DEFAULT 'pending',
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    read_at TIMESTAMPTZ,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    created_month DATE GENERATED ALWAYS AS (date_trunc('month', created_at)::date) STORED
);

-- 创建索引模板
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);
CREATE INDEX idx_notifications_tenant_status ON notifications(tenant_id, status);

-- 创建通知表的函数（复用审计日志的模式）
CREATE OR REPLACE FUNCTION create_notifications_table(target_date DATE)
RETURNS TEXT AS $$
DECLARE
    table_name TEXT;
    start_date DATE;
    end_date DATE;
    sql_text TEXT;
BEGIN
    table_name := 'notifications_' || to_char(target_date, 'YYYY_MM');
    start_date := date_trunc('month', target_date)::date;
    end_date := (date_trunc('month', target_date) + interval '1 month')::date;

    IF EXISTS (SELECT 1 FROM information_schema.tables
               WHERE table_name = table_name AND table_schema = 'public') THEN
        RETURN 'Table ' || table_name || ' already exists';
    END IF;

    sql_text := format('
        CREATE TABLE %I (
            CHECK (created_at >= %L AND created_at < %L)
        ) INHERITS (notifications)',
        table_name, start_date, end_date
    );

    EXECUTE sql_text;

    -- 创建表特定的索引
    EXECUTE format('CREATE INDEX idx_%I_created_at ON %I(created_at)', table_name, table_name);
    EXECUTE format('CREATE INDEX idx_%I_status_created ON %I(status, created_at)', table_name, table_name);
    EXECUTE format('CREATE INDEX idx_%I_user_created ON %I(user_id, created_at)', table_name, table_name);

    -- 创建触发器函数（如果不存在）
    IF NOT EXISTS (SELECT 1 FROM pg_proc WHERE proname = 'notifications_insert_trigger') THEN
        EXECUTE '
        CREATE OR REPLACE FUNCTION notifications_insert_trigger()
        RETURNS TRIGGER AS $trigger$
        DECLARE
            table_name TEXT;
        BEGIN
            table_name := ''notifications_'' || to_char(NEW.created_at, ''YYYY_MM'');

            EXECUTE format(''INSERT INTO %I VALUES ($1.*)'', table_name) USING NEW;

            RETURN NULL;
        EXCEPTION
            WHEN undefined_table THEN
                PERFORM create_notifications_table(NEW.created_at::date);
                EXECUTE format(''INSERT INTO %I VALUES ($1.*)'', table_name) USING NEW;
                RETURN NULL;
        END;
        $trigger$ LANGUAGE plpgsql;
        ';

        EXECUTE '
        CREATE TRIGGER notifications_insert_trigger
            BEFORE INSERT ON notifications
            FOR EACH ROW EXECUTE FUNCTION notifications_insert_trigger();
        ';
    END IF;

    RETURN 'Created table ' || table_name;
END;
$$ LANGUAGE plpgsql;

-- 初始化当月和下月的通知表
SELECT create_notifications_table(CURRENT_DATE);
SELECT create_notifications_table(CURRENT_DATE + interval '1 month');

-- 创建清理旧通知记录的函数（保留6个月）
CREATE OR REPLACE FUNCTION cleanup_old_notifications(retain_months INTEGER DEFAULT 6)
RETURNS TEXT AS $$
DECLARE
    cutoff_date DATE;
    table_record RECORD;
    table_name TEXT;
    result_text TEXT := '';
BEGIN
    cutoff_date := (date_trunc('month', CURRENT_DATE) - (retain_months || ' months')::interval)::date;

    FOR table_record IN
        SELECT schemaname, tablename
        FROM pg_tables
        WHERE tablename LIKE 'notifications_%'
        AND tablename ~ '^notifications_[0-9]{4}_[0-9]{2}$'
        AND to_date(substring(tablename from 15), 'YYYY_MM') < cutoff_date
    LOOP
        table_name := table_record.tablename;
        EXECUTE format('DROP TABLE IF EXISTS %I CASCADE', table_name);
        result_text := result_text || 'Dropped table ' || table_name || '; ';
    END LOOP;

    IF result_text = '' THEN
        result_text := 'No notification tables to cleanup';
    END IF;

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;
```

## 10. 系统配置表设计

### 10.1 系统配置表 (system_configs)
```sql
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    category VARCHAR(50) NOT NULL,
    key VARCHAR(100) NOT NULL,
    value JSONB NOT NULL,
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE, -- 是否可以通过API公开访问
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(tenant_id, category, key)
);

-- 索引
CREATE INDEX idx_system_configs_tenant_category ON system_configs(tenant_id, category);
CREATE INDEX idx_system_configs_public ON system_configs(is_public) WHERE is_public = TRUE;

-- 触发器
CREATE TRIGGER update_system_configs_updated_at
    BEFORE UPDATE ON system_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 11. 数据库视图设计

### 11.1 用户权限视图
```sql
CREATE VIEW user_permissions_view AS
SELECT
    ut.user_id,
    ut.tenant_id,
    u.username,
    u.email,
    t.name as tenant_name,
    ut.role,
    r.permissions as role_permissions,
    ut.permissions as user_permissions,
    ut.status as user_tenant_status,
    u.status as user_status
FROM user_tenants ut
JOIN users u ON ut.user_id = u.id
JOIN tenants t ON ut.tenant_id = t.id
LEFT JOIN roles r ON r.tenant_id = ut.tenant_id AND r.name = ut.role
WHERE ut.status = 'active' AND u.status = 'active' AND t.status = 'active';
```

### 11.2 租户统计视图
```sql
CREATE VIEW tenant_statistics_view AS
SELECT
    t.id as tenant_id,
    t.name as tenant_name,
    t.status as tenant_status,
    COUNT(DISTINCT ut.user_id) as total_users,
    COUNT(DISTINCT CASE WHEN ut.status = 'active' THEN ut.user_id END) as active_users,
    COUNT(DISTINCT oc.id) as total_oauth_clients,
    COUNT(DISTINCT r.id) as total_roles,
    t.created_at
FROM tenants t
LEFT JOIN user_tenants ut ON t.id = ut.tenant_id
LEFT JOIN oauth_clients oc ON t.id = oc.tenant_id
LEFT JOIN roles r ON t.id = r.tenant_id
GROUP BY t.id, t.name, t.status, t.created_at;
```

## 12. 数据库函数和存储过程

### 12.1 用户登录验证函数
```sql
CREATE OR REPLACE FUNCTION authenticate_user(
    p_username VARCHAR,
    p_password_hash VARCHAR,
    p_tenant_id UUID DEFAULT NULL
)
RETURNS TABLE(
    user_id UUID,
    tenant_id UUID,
    role VARCHAR,
    permissions JSONB,
    mfa_required BOOLEAN
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        u.id as user_id,
        ut.tenant_id,
        ut.role,
        COALESCE(r.permissions, '{}'::jsonb) || COALESCE(ut.permissions, '{}'::jsonb) as permissions,
        u.mfa_enabled as mfa_required
    FROM users u
    JOIN user_tenants ut ON u.id = ut.user_id
    LEFT JOIN roles r ON r.tenant_id = ut.tenant_id AND r.name = ut.role
    WHERE u.username = p_username
      AND u.password_hash = p_password_hash
      AND u.status = 'active'
      AND ut.status = 'active'
      AND (p_tenant_id IS NULL OR ut.tenant_id = p_tenant_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 12.2 权限检查函数
```sql
CREATE OR REPLACE FUNCTION check_permission(
    p_user_id UUID,
    p_tenant_id UUID,
    p_permission VARCHAR
)
RETURNS BOOLEAN AS $$
DECLARE
    user_perms JSONB;
BEGIN
    SELECT COALESCE(r.permissions, '{}'::jsonb) || COALESCE(ut.permissions, '{}'::jsonb)
    INTO user_perms
    FROM user_tenants ut
    LEFT JOIN roles r ON r.tenant_id = ut.tenant_id AND r.name = ut.role
    WHERE ut.user_id = p_user_id
      AND ut.tenant_id = p_tenant_id
      AND ut.status = 'active';

    IF user_perms IS NULL THEN
        RETURN FALSE;
    END IF;

    -- 检查权限是否存在于用户权限中
    RETURN user_perms ? p_permission OR user_perms ? '*';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 13. 数据库备份和恢复策略

### 13.1 备份策略
```bash
#!/bin/bash
# 全量备份脚本
BACKUP_DIR="/backup/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="auth_db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
pg_dump -h localhost -U auth_user -d $DB_NAME \
    --format=custom \
    --compress=9 \
    --file=$BACKUP_DIR/auth_db_full_$DATE.dump

# 增量备份（WAL归档）
pg_basebackup -h localhost -U auth_user \
    --pgdata=$BACKUP_DIR/base_$DATE \
    --format=tar \
    --compress=9 \
    --progress \
    --verbose
```

### 13.2 恢复策略
```bash
#!/bin/bash
# 恢复脚本
BACKUP_FILE="/backup/postgresql/auth_db_full_20240101_120000.dump"
DB_NAME="auth_db_restore"

# 创建新数据库
createdb -h localhost -U auth_user $DB_NAME

# 恢复数据
pg_restore -h localhost -U auth_user \
    --dbname=$DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    $BACKUP_FILE
```

## 14. 数据库初始化和维护

### 14.1 数据库初始化脚本
```sql
-- 数据库初始化脚本
-- 执行顺序：枚举类型 -> 函数 -> 表 -> 索引 -> 触发器 -> 初始数据

-- 1. 创建所有枚举类型
DO $$
BEGIN
    -- 用户状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_status_enum') THEN
        CREATE TYPE user_status_enum AS ENUM ('pending', 'active', 'locked', 'disabled', 'deleted');
    END IF;

    -- 租户状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_status_enum') THEN
        CREATE TYPE tenant_status_enum AS ENUM ('active', 'suspended', 'deleted');
    END IF;

    -- 租户等级枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'tenant_tier_enum') THEN
        CREATE TYPE tenant_tier_enum AS ENUM ('trial', 'standard', 'premium', 'enterprise');
    END IF;

    -- 用户租户状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_tenant_status_enum') THEN
        CREATE TYPE user_tenant_status_enum AS ENUM ('pending', 'active', 'inactive', 'suspended');
    END IF;

    -- 邀请状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'invitation_status_enum') THEN
        CREATE TYPE invitation_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'expired', 'cancelled');
    END IF;

    -- 申请状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'application_status_enum') THEN
        CREATE TYPE application_status_enum AS ENUM ('pending', 'approved', 'rejected', 'expired', 'cancelled');
    END IF;

    -- OAuth客户端类型枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'oauth_client_type_enum') THEN
        CREATE TYPE oauth_client_type_enum AS ENUM ('public', 'confidential');
    END IF;

    -- 通知类型枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_type_enum') THEN
        CREATE TYPE notification_type_enum AS ENUM ('email', 'sms', 'push', 'in_app');
    END IF;

    -- 通知状态枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'notification_status_enum') THEN
        CREATE TYPE notification_status_enum AS ENUM ('pending', 'sent', 'delivered', 'failed', 'read');
    END IF;

    -- 资源类型枚举
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'resource_type_enum') THEN
        CREATE TYPE resource_type_enum AS ENUM ('api', 'page', 'button', 'data', 'file');
    END IF;
END $$;

-- 2. 创建通用函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 3. 初始化分表系统
SELECT create_audit_logs_table(CURRENT_DATE);
SELECT create_audit_logs_table(CURRENT_DATE + interval '1 month');
SELECT create_notifications_table(CURRENT_DATE);
SELECT create_notifications_table(CURRENT_DATE + interval '1 month');

-- 4. 创建系统管理员用户
INSERT INTO users (id, username, email, password_hash, name, status, email_verified)
VALUES (
    gen_random_uuid(),
    'admin',
    '<EMAIL>',
    '$2a$12$dummy.hash.for.initial.admin', -- 需要替换为实际的密码哈希
    'System Administrator',
    'active',
    true
) ON CONFLICT (username) DO NOTHING;

-- 5. 创建默认系统租户
INSERT INTO tenants (id, name, domain, description, status, tier)
VALUES (
    gen_random_uuid(),
    'System',
    'system.local',
    'System default tenant',
    'active',
    'enterprise'
) ON CONFLICT (domain) DO NOTHING;
```

### 14.2 定期维护任务
```sql
-- 创建数据库维护任务函数
CREATE OR REPLACE FUNCTION database_maintenance()
RETURNS TEXT AS $$
DECLARE
    result_text TEXT := '';
    audit_result TEXT;
    notification_result TEXT;
    stats_result TEXT;
BEGIN
    -- 1. 创建下个月的分表
    BEGIN
        SELECT create_audit_logs_table(CURRENT_DATE + interval '2 months') INTO audit_result;
        result_text := result_text || 'Audit: ' || audit_result || '; ';
    EXCEPTION
        WHEN OTHERS THEN
            result_text := result_text || 'Audit table creation failed: ' || SQLERRM || '; ';
    END;

    BEGIN
        SELECT create_notifications_table(CURRENT_DATE + interval '2 months') INTO notification_result;
        result_text := result_text || 'Notification: ' || notification_result || '; ';
    EXCEPTION
        WHEN OTHERS THEN
            result_text := result_text || 'Notification table creation failed: ' || SQLERRM || '; ';
    END;

    -- 2. 清理过期数据
    BEGIN
        -- 清理过期的邀请
        UPDATE invitations
        SET status = 'expired', updated_at = NOW()
        WHERE status = 'pending' AND expires_at < NOW();

        -- 清理过期的申请
        UPDATE applications
        SET status = 'expired', updated_at = NOW()
        WHERE status = 'pending' AND expires_at < NOW();

        -- 清理过期的授权码
        DELETE FROM oauth_authorization_codes WHERE expires_at < NOW();

        -- 清理过期的访问令牌
        UPDATE oauth_access_tokens
        SET revoked_at = NOW()
        WHERE expires_at < NOW() AND revoked_at IS NULL;

        result_text := result_text || 'Expired data cleaned; ';
    EXCEPTION
        WHEN OTHERS THEN
            result_text := result_text || 'Data cleanup failed: ' || SQLERRM || '; ';
    END;

    -- 3. 更新统计信息
    BEGIN
        ANALYZE;
        result_text := result_text || 'Statistics updated; ';
    EXCEPTION
        WHEN OTHERS THEN
            result_text := result_text || 'Statistics update failed: ' || SQLERRM || '; ';
    END;

    -- 4. 记录维护日志
    INSERT INTO audit_logs (action, resource_type, details, timestamp)
    VALUES (
        'system.maintenance',
        'database',
        jsonb_build_object('result', result_text, 'timestamp', NOW()),
        NOW()
    );

    RETURN result_text;
END;
$$ LANGUAGE plpgsql;

-- 创建性能监控函数
CREATE OR REPLACE FUNCTION get_database_performance_stats()
RETURNS TABLE(
    metric_name TEXT,
    metric_value TEXT,
    description TEXT
) AS $$
BEGIN
    -- 数据库大小
    RETURN QUERY SELECT
        'database_size'::TEXT,
        pg_size_pretty(pg_database_size(current_database())),
        'Total database size'::TEXT;

    -- 连接数
    RETURN QUERY SELECT
        'active_connections'::TEXT,
        COUNT(*)::TEXT,
        'Number of active connections'::TEXT
    FROM pg_stat_activity
    WHERE state = 'active';

    -- 最大连接数
    RETURN QUERY SELECT
        'max_connections'::TEXT,
        setting,
        'Maximum allowed connections'::TEXT
    FROM pg_settings
    WHERE name = 'max_connections';

    -- 缓存命中率
    RETURN QUERY SELECT
        'cache_hit_ratio'::TEXT,
        ROUND(
            100.0 * sum(blks_hit) / (sum(blks_hit) + sum(blks_read)), 2
        )::TEXT || '%',
        'Buffer cache hit ratio'::TEXT
    FROM pg_stat_database
    WHERE datname = current_database();

    -- 最慢的查询
    RETURN QUERY SELECT
        'slowest_query_time'::TEXT,
        ROUND(max_time::numeric, 2)::TEXT || 'ms',
        'Slowest query execution time'::TEXT
    FROM pg_stat_statements
    ORDER BY max_time DESC
    LIMIT 1;

    -- 审计日志表数量
    RETURN QUERY SELECT
        'audit_tables_count'::TEXT,
        COUNT(*)::TEXT,
        'Number of audit log tables'::TEXT
    FROM pg_tables
    WHERE tablename LIKE 'audit_logs_%';

    -- 通知表数量
    RETURN QUERY SELECT
        'notification_tables_count'::TEXT,
        COUNT(*)::TEXT,
        'Number of notification tables'::TEXT
    FROM pg_tables
    WHERE tablename LIKE 'notifications_%';
END;
$$ LANGUAGE plpgsql;

-- 使用pg_cron创建定期任务（需要安装pg_cron扩展）
/*
-- 每月1号凌晨2点执行维护任务
SELECT cron.schedule('monthly-maintenance', '0 2 1 * *', 'SELECT database_maintenance();');

-- 每天凌晨3点清理旧的审计日志（保留24个月）
SELECT cron.schedule('cleanup-audit-logs', '0 3 * * *', 'SELECT cleanup_old_audit_logs(24);');

-- 每天凌晨4点清理旧的通知记录（保留6个月）
SELECT cron.schedule('cleanup-notifications', '0 4 * * *', 'SELECT cleanup_old_notifications(6);');

-- 查看所有定时任务
SELECT * FROM cron.job;

-- 删除定时任务
-- SELECT cron.unschedule('monthly-maintenance');
*/
```

### 14.3 监控和告警查询
```sql
-- 监控查询示例

-- 1. 检查表空间使用情况
SELECT
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
    pg_size_pretty(pg_relation_size(schemaname||'.'||tablename)) as table_size,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) - pg_relation_size(schemaname||'.'||tablename)) as index_size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
LIMIT 10;

-- 2. 检查长时间运行的查询
SELECT
    pid,
    now() - pg_stat_activity.query_start AS duration,
    query,
    state
FROM pg_stat_activity
WHERE (now() - pg_stat_activity.query_start) > interval '5 minutes'
AND state = 'active';

-- 3. 检查锁等待情况
SELECT
    blocked_locks.pid AS blocked_pid,
    blocked_activity.usename AS blocked_user,
    blocking_locks.pid AS blocking_pid,
    blocking_activity.usename AS blocking_user,
    blocked_activity.query AS blocked_statement,
    blocking_activity.query AS current_statement_in_blocking_process
FROM pg_catalog.pg_locks blocked_locks
JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype
    AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
    AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
    AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page
    AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple
    AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid
    AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid
    AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid
    AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid
    AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid
    AND blocking_locks.pid != blocked_locks.pid
JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
WHERE NOT blocked_locks.granted;

-- 4. 检查分表创建状态
SELECT
    'audit_logs_' || to_char(generate_series, 'YYYY_MM') as expected_table,
    CASE
        WHEN EXISTS (
            SELECT 1 FROM pg_tables
            WHERE tablename = 'audit_logs_' || to_char(generate_series, 'YYYY_MM')
        ) THEN 'EXISTS'
        ELSE 'MISSING'
    END as status
FROM generate_series(
    date_trunc('month', CURRENT_DATE - interval '2 months'),
    date_trunc('month', CURRENT_DATE + interval '2 months'),
    interval '1 month'
) as generate_series;
```

这个PostgreSQL 17数据库设计充分利用了PostgreSQL的企业级特性，特别是在多租户支持、安全性和性能方面，为统一认证授权系统提供了强大的数据存储基础。设计包含了完整的表结构、索引、约束、视图、函数、继承表分表策略、自动化维护任务和监控查询，确保系统的数据完整性、安全性和高性能。

**关键特性总结：**
1. **两级认证体系支持** - 用户全局化，租户关系独立管理
2. **继承表分表** - 审计日志和通知记录按月自动分表
3. **自动化维护** - 自动创建新月表，清理过期数据
4. **性能优化** - 合理的索引设计和查询优化
5. **安全防护** - 行级安全策略和数据加密
6. **监控告警** - 完整的性能监控和异常检测
