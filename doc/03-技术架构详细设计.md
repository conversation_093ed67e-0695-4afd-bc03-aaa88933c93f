# 03-统一认证授权系统 - 技术架构详细设计

## 1. 技术选型详细说明

### 1.1 Spring Security 6.x + Spring Authorization Server 1.x

#### 选择理由
- **标准协议支持**: 完整支持OAuth 2.0、OpenID Connect、SAML 2.0
- **企业级安全**: 成熟的安全框架，经过大量企业验证
- **Spring生态**: 与Spring Boot无缝集成，开发效率高
- **社区支持**: 活跃的社区和丰富的文档资源
- **可扩展性**: 灵活的扩展机制，支持自定义认证流程

#### 核心组件架构
```mermaid
graph TB
    subgraph "Spring Authorization Server"
        AS_CORE[Authorization Server Core]
        TOKEN_ENDPOINT[Token Endpoint]
        AUTH_ENDPOINT[Authorization Endpoint]
        JWKS_ENDPOINT[JWKS Endpoint]
        USERINFO_ENDPOINT[UserInfo Endpoint]
    end

    subgraph "Spring Security"
        SEC_FILTER[Security Filter Chain]
        AUTH_MANAGER[Authentication Manager]
        AUTH_PROVIDER[Authentication Provider]
        USER_DETAILS[UserDetailsService]
        ACCESS_DECISION[Access Decision Manager]
    end

    subgraph "Custom Components"
        CUSTOM_USER_SERVICE[Custom UserDetailsService]
        MFA_PROVIDER[MFA Authentication Provider]
        TENANT_FILTER[Tenant Context Filter]
        AUDIT_FILTER[Audit Logging Filter]
    end

    AS_CORE --> TOKEN_ENDPOINT
    AS_CORE --> AUTH_ENDPOINT
    AS_CORE --> JWKS_ENDPOINT
    AS_CORE --> USERINFO_ENDPOINT

    SEC_FILTER --> AUTH_MANAGER
    AUTH_MANAGER --> AUTH_PROVIDER
    AUTH_PROVIDER --> USER_DETAILS

    USER_DETAILS --> CUSTOM_USER_SERVICE
    AUTH_PROVIDER --> MFA_PROVIDER
    SEC_FILTER --> TENANT_FILTER
    SEC_FILTER --> AUDIT_FILTER
```

### 1.2 PostgreSQL 17 数据库架构

#### 多租户数据隔离实现
```java
// 两级认证上下文设置
@Component
public class AuthenticationContext {
    private static final ThreadLocal<String> currentUser = new ThreadLocal<>();
    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();

    public static void setCurrentUser(String userId) {
        currentUser.set(userId);
    }

    public static String getCurrentUser() {
        return currentUser.get();
    }

    public static void setCurrentTenant(String tenantId) {
        currentTenant.set(tenantId);
    }

    public static String getCurrentTenant() {
        return currentTenant.get();
    }

    public static void clear() {
        currentUser.remove();
        currentTenant.remove();
    }
}

// 数据源配置
@Configuration
public class DatabaseConfig {
    
    @Bean
    @Primary
    public DataSource dataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("****************************************");
        config.setUsername("auth_user");
        config.setPassword("password");
        config.setMaximumPoolSize(20);
        config.setMinimumIdle(5);
        config.setConnectionInitSql("SET app.current_tenant_id = ''");
        return new HikariDataSource(config);
    }
}

// 两级认证拦截器
@Component
public class AuthenticationInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtTokenProvider jwtTokenProvider;

    @Override
    public boolean preHandle(HttpServletRequest request,
                           HttpServletResponse response,
                           Object handler) throws Exception {
        String token = extractToken(request);
        if (token != null) {
            Jwt jwt = jwtTokenProvider.parseToken(token);
            String userId = jwt.getSubject();
            String tenantId = jwt.getClaimAsString("tenant_id");

            // 设置用户和租户上下文
            AuthenticationContext.setCurrentUser(userId);
            if (tenantId != null) {
                AuthenticationContext.setCurrentTenant(tenantId);
                // 设置PostgreSQL会话变量
                setDatabaseContext(userId, tenantId);
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request,
                              HttpServletResponse response,
                              Object handler, Exception ex) {
        AuthenticationContext.clear();
    }

    private void setDatabaseContext(String userId, String tenantId) {
        // 设置PostgreSQL会话变量用于行级安全
        jdbcTemplate.execute("SET app.current_user_id = '" + userId + "'");
        jdbcTemplate.execute("SET app.current_tenant_id = '" + tenantId + "'");
    }
}
```

### 1.3 Redis 7.0 缓存架构

#### 缓存策略设计
```java
@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager(RedisConnectionFactory connectionFactory) {
        RedisCacheConfiguration config = RedisCacheConfiguration.defaultCacheConfig()
            .entryTtl(Duration.ofMinutes(30))
            .serializeKeysWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new StringRedisSerializer()))
            .serializeValuesWith(RedisSerializationContext.SerializationPair
                .fromSerializer(new GenericJackson2JsonRedisSerializer()));
        
        return RedisCacheManager.builder(connectionFactory)
            .cacheDefaults(config)
            .transactionAware()
            .build();
    }
    
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new GenericJackson2JsonRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new GenericJackson2JsonRedisSerializer());
        return template;
    }
}

// 权限缓存服务
@Service
public class PermissionCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    private static final String PERMISSION_KEY_PREFIX = "permission:";
    private static final String USER_ROLE_KEY_PREFIX = "user_role:";
    
    @Cacheable(value = "user-permissions", key = "#tenantId + ':' + #userId")
    public Set<String> getUserPermissions(String tenantId, String userId) {
        // 从数据库查询用户权限
        return permissionRepository.findUserPermissions(tenantId, userId);
    }
    
    @CacheEvict(value = "user-permissions", key = "#tenantId + ':' + #userId")
    public void evictUserPermissions(String tenantId, String userId) {
        // 清除用户权限缓存
    }
}
```

## 2. 微服务架构设计

### 2.1 服务拆分策略

#### 认证服务 (auth-service)
```java
@SpringBootApplication
@EnableAuthorizationServer
public class AuthServiceApplication {
    public static void main(String[] args) {
        SpringApplication.run(AuthServiceApplication.class, args);
    }
}

@Configuration
public class AuthorizationServerConfig {
    
    @Bean
    public RegisteredClientRepository registeredClientRepository() {
        RegisteredClient client = RegisteredClient.withId(UUID.randomUUID().toString())
            .clientId("web-client")
            .clientSecret("{noop}secret")
            .clientAuthenticationMethod(ClientAuthenticationMethod.CLIENT_SECRET_BASIC)
            .authorizationGrantType(AuthorizationGrantType.AUTHORIZATION_CODE)
            .authorizationGrantType(AuthorizationGrantType.REFRESH_TOKEN)
            .redirectUri("http://localhost:3000/callback")
            .scope(OidcScopes.OPENID)
            .scope(OidcScopes.PROFILE)
            .scope("read")
            .scope("write")
            .clientSettings(ClientSettings.builder()
                .requireAuthorizationConsent(false)
                .build())
            .tokenSettings(TokenSettings.builder()
                .accessTokenTimeToLive(Duration.ofHours(1))
                .refreshTokenTimeToLive(Duration.ofDays(7))
                .build())
            .build();
        
        return new InMemoryRegisteredClientRepository(client);
    }
    
    @Bean
    public JWKSource<SecurityContext> jwkSource() {
        KeyPair keyPair = generateRsaKey();
        RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();
        RSAKey rsaKey = new RSAKey.Builder(publicKey)
            .privateKey(privateKey)
            .keyID(UUID.randomUUID().toString())
            .build();
        JWKSet jwkSet = new JWKSet(rsaKey);
        return new ImmutableJWKSet<>(jwkSet);
    }
}
```

#### 用户管理服务 (user-service)
```java
@RestController
@RequestMapping("/api/v1/users")
@Validated
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserDto>> register(
            @Valid @RequestBody UserRegistrationRequest request) {
        UserDto user = userService.register(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
    
    @GetMapping("/profile")
    @PreAuthorize("hasAuthority('SCOPE_profile')")
    public ResponseEntity<ApiResponse<UserDto>> getProfile(Authentication authentication) {
        String userId = authentication.getName();
        UserDto user = userService.getUserById(userId);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
    
    @PutMapping("/profile")
    @PreAuthorize("hasAuthority('SCOPE_profile')")
    public ResponseEntity<ApiResponse<UserDto>> updateProfile(
            @Valid @RequestBody UserUpdateRequest request,
            Authentication authentication) {
        String userId = authentication.getName();
        UserDto user = userService.updateUser(userId, request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
}

@Service
@Transactional
public class UserService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private UserTenantRepository userTenantRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private NotificationService notificationService;

    public UserDto register(UserRegistrationRequest request) {
        // 验证用户名和邮箱唯一性
        if (userRepository.existsByUsername(request.getUsername())) {
            throw new BusinessException("用户名已存在");
        }

        if (userRepository.existsByEmail(request.getEmail())) {
            throw new BusinessException("邮箱已被注册");
        }

        // 创建全局用户（无租户信息）
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPasswordHash(passwordEncoder.encode(request.getPassword()));
        user.setName(request.getName());
        user.setStatus(UserStatus.PENDING);

        user = userRepository.save(user);

        // 发送验证邮件
        notificationService.sendEmailVerification(user);

        return UserMapper.toDto(user);
    }

    public List<UserTenantDto> getUserTenants(String userId) {
        List<UserTenant> userTenants = userTenantRepository.findByUserIdAndStatus(
            userId, UserTenantStatus.ACTIVE);
        return userTenants.stream()
            .map(UserTenantMapper::toDto)
            .collect(Collectors.toList());
    }

    public UserTenantDto switchTenant(String userId, String tenantId) {
        UserTenant userTenant = userTenantRepository.findByUserIdAndTenantId(userId, tenantId)
            .orElseThrow(() -> new BusinessException("用户不属于该租户"));

        if (userTenant.getStatus() != UserTenantStatus.ACTIVE) {
            throw new BusinessException("用户在该租户中的状态不是激活状态");
        }

        // 更新最后访问时间
        userTenant.setLastAccessedAt(Instant.now());
        userTenantRepository.save(userTenant);

        return UserTenantMapper.toDto(userTenant);
    }
}
```

#### 租户管理服务 (tenant-service)
```java
@RestController
@RequestMapping("/api/v1/tenants")
public class TenantController {
    
    @Autowired
    private TenantService tenantService;
    
    @PostMapping
    @PreAuthorize("hasAuthority('SCOPE_tenant:create')")
    public ResponseEntity<ApiResponse<TenantDto>> createTenant(
            @Valid @RequestBody TenantCreateRequest request,
            Authentication authentication) {
        String userId = authentication.getName();
        TenantDto tenant = tenantService.createTenant(request, userId);
        return ResponseEntity.ok(ApiResponse.success(tenant));
    }
    
    @PostMapping("/{tenantId}/invitations")
    @PreAuthorize("hasAuthority('SCOPE_tenant:invite') and @tenantSecurityService.canManageTenant(#tenantId, authentication.name)")
    public ResponseEntity<ApiResponse<InvitationDto>> inviteUser(
            @PathVariable String tenantId,
            @Valid @RequestBody InvitationRequest request,
            Authentication authentication) {
        String inviterId = authentication.getName();
        InvitationDto invitation = tenantService.inviteUser(tenantId, request, inviterId);
        return ResponseEntity.ok(ApiResponse.success(invitation));
    }
}
```

### 2.2 服务间通信

#### Feign客户端配置
```java
@FeignClient(name = "user-service", url = "${services.user-service.url}")
public interface UserServiceClient {
    
    @GetMapping("/api/v1/users/{userId}")
    ApiResponse<UserDto> getUserById(@PathVariable String userId);
    
    @PostMapping("/api/v1/users/{userId}/verify-email")
    ApiResponse<Void> verifyEmail(@PathVariable String userId, @RequestParam String token);
}

@Configuration
public class FeignConfig {
    
    @Bean
    public RequestInterceptor requestInterceptor() {
        return requestTemplate -> {
            // 传递租户上下文
            String tenantId = TenantContext.getCurrentTenant();
            if (tenantId != null) {
                requestTemplate.header("X-Tenant-ID", tenantId);
            }
            
            // 传递认证信息
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getCredentials() instanceof String) {
                requestTemplate.header("Authorization", "Bearer " + authentication.getCredentials());
            }
        };
    }
}
```

## 3. 安全架构实现

### 3.1 JWT令牌设计
```java
@Component
public class JwtTokenProvider {
    
    private final JWKSource<SecurityContext> jwkSource;
    private final JwtEncoder jwtEncoder;
    private final JwtDecoder jwtDecoder;
    
    public String generateAccessToken(Authentication authentication, String tenantId) {
        Instant now = Instant.now();
        Instant expiry = now.plus(1, ChronoUnit.HOURS);
        
        JwtClaimsSet claims = JwtClaimsSet.builder()
            .issuer("https://auth.example.com")
            .subject(authentication.getName())
            .audience(Arrays.asList("web-client", "mobile-client"))
            .issuedAt(now)
            .expiresAt(expiry)
            .claim("tenant_id", tenantId)
            .claim("scope", getScopes(authentication))
            .claim("authorities", getAuthorities(authentication))
            .build();
        
        return jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
    }
    
    public boolean validateToken(String token) {
        try {
            Jwt jwt = jwtDecoder.decode(token);
            return !jwt.getExpiresAt().isBefore(Instant.now());
        } catch (Exception e) {
            return false;
        }
    }
}
```

### 3.2 多因子认证实现
```java
@Component
public class MfaAuthenticationProvider implements AuthenticationProvider {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private TotpService totpService;
    
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        MfaAuthenticationToken mfaToken = (MfaAuthenticationToken) authentication;
        
        String username = mfaToken.getPrincipal().toString();
        String mfaCode = mfaToken.getCredentials().toString();
        
        User user = userService.findByUsername(username);
        if (user == null) {
            throw new BadCredentialsException("用户不存在");
        }
        
        if (!user.isMfaEnabled()) {
            throw new BadCredentialsException("用户未启用MFA");
        }
        
        boolean isValid = totpService.verifyCode(user.getMfaSecret(), mfaCode);
        if (!isValid) {
            throw new BadCredentialsException("MFA验证码无效");
        }
        
        Collection<GrantedAuthority> authorities = getUserAuthorities(user);
        return new UsernamePasswordAuthenticationToken(username, null, authorities);
    }
    
    @Override
    public boolean supports(Class<?> authentication) {
        return MfaAuthenticationToken.class.isAssignableFrom(authentication);
    }
}

@Service
public class TotpService {
    
    private static final int TIME_STEP = 30;
    private static final int DIGITS = 6;
    
    public String generateSecret() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[20];
        random.nextBytes(bytes);
        return Base32.encode(bytes);
    }
    
    public boolean verifyCode(String secret, String code) {
        long timeWindow = System.currentTimeMillis() / 1000 / TIME_STEP;
        
        // 允许前后一个时间窗口的误差
        for (int i = -1; i <= 1; i++) {
            String expectedCode = generateCode(secret, timeWindow + i);
            if (code.equals(expectedCode)) {
                return true;
            }
        }
        return false;
    }
    
    private String generateCode(String secret, long timeWindow) {
        byte[] key = Base32.decode(secret);
        byte[] data = ByteBuffer.allocate(8).putLong(timeWindow).array();
        
        try {
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(new SecretKeySpec(key, "HmacSHA1"));
            byte[] hash = mac.doFinal(data);
            
            int offset = hash[hash.length - 1] & 0x0F;
            int truncatedHash = ((hash[offset] & 0x7F) << 24) |
                               ((hash[offset + 1] & 0xFF) << 16) |
                               ((hash[offset + 2] & 0xFF) << 8) |
                               (hash[offset + 3] & 0xFF);
            
            int code = truncatedHash % (int) Math.pow(10, DIGITS);
            return String.format("%0" + DIGITS + "d", code);
        } catch (Exception e) {
            throw new RuntimeException("生成TOTP代码失败", e);
        }
    }
}
```

## 4. 监控和可观测性

### 4.1 Micrometer + Prometheus集成
```java
@Configuration
public class MetricsConfig {
    
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> registry.config().commonTags("application", "auth-service");
    }
    
    @Bean
    public TimedAspect timedAspect(MeterRegistry registry) {
        return new TimedAspect(registry);
    }
}

@Component
public class AuthenticationMetrics {
    
    private final Counter loginAttempts;
    private final Counter loginSuccesses;
    private final Counter loginFailures;
    private final Timer authenticationTime;
    
    public AuthenticationMetrics(MeterRegistry meterRegistry) {
        this.loginAttempts = Counter.builder("auth.login.attempts")
            .description("Total login attempts")
            .register(meterRegistry);
        
        this.loginSuccesses = Counter.builder("auth.login.successes")
            .description("Successful logins")
            .register(meterRegistry);
        
        this.loginFailures = Counter.builder("auth.login.failures")
            .description("Failed logins")
            .register(meterRegistry);
        
        this.authenticationTime = Timer.builder("auth.authentication.time")
            .description("Authentication processing time")
            .register(meterRegistry);
    }
    
    public void recordLoginAttempt() {
        loginAttempts.increment();
    }
    
    public void recordLoginSuccess() {
        loginSuccesses.increment();
    }
    
    public void recordLoginFailure() {
        loginFailures.increment();
    }
    
    public Timer.Sample startAuthenticationTimer() {
        return Timer.start(authenticationTime);
    }
}
```

这个技术架构详细设计为统一认证授权系统提供了完整的技术实现指导，确保系统的安全性、可扩展性和可维护性。
