# 05-统一认证授权系统 - API设计规范

## 1. API设计原则

### 1.1 RESTful设计规范
- **资源导向**: URL表示资源，HTTP方法表示操作
- **无状态**: 每个请求包含所有必要信息
- **统一接口**: 使用标准HTTP方法和状态码
- **分层系统**: 支持缓存、负载均衡等中间层
- **按需代码**: 支持客户端扩展（可选）

### 1.2 URL设计规范
```
基础URL: https://api.auth.example.com
版本控制: /api/v1/
资源命名: 使用复数名词，小写字母，连字符分隔

示例:
GET /api/v1/users              # 获取用户列表
GET /api/v1/users/{id}         # 获取特定用户
POST /api/v1/users             # 创建用户
PUT /api/v1/users/{id}         # 更新用户
DELETE /api/v1/users/{id}      # 删除用户
```

### 1.3 HTTP状态码规范
```
200 OK                 - 请求成功
201 Created           - 资源创建成功
204 No Content        - 请求成功，无返回内容
400 Bad Request       - 请求参数错误
401 Unauthorized      - 未认证
403 Forbidden         - 无权限
404 Not Found         - 资源不存在
409 Conflict          - 资源冲突
422 Unprocessable Entity - 请求格式正确但语义错误
429 Too Many Requests - 请求频率超限
500 Internal Server Error - 服务器内部错误
```

### 1.4 统一响应格式
```json
{
  "code": 200,
  "message": "Success",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid"
}

// 错误响应格式
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "uuid"
}
```

## 2. 认证授权API

### 2.1 OAuth 2.0 授权端点

#### 授权码获取
```http
GET /oauth2/authorize?
    response_type=code&
    client_id={client_id}&
    redirect_uri={redirect_uri}&
    scope={scope}&
    state={state}&
    code_challenge={code_challenge}&
    code_challenge_method=S256

Response: 302 Redirect
Location: {redirect_uri}?code={authorization_code}&state={state}
```

#### 访问令牌获取（两级认证）
```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {base64(client_id:client_secret)}

grant_type=authorization_code&
code={authorization_code}&
redirect_uri={redirect_uri}&
code_verifier={code_verifier}&
tenant_id={tenant_id}

Response:
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "def50200...",
  "scope": "openid profile email",
  "tenant_context": {
    "tenant_id": "tenant-uuid",
    "tenant_name": "示例企业",
    "user_role": "admin",
    "permissions": ["user:read", "user:write"]
  }
}
```

#### 令牌刷新
```http
POST /oauth2/token
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {base64(client_id:client_secret)}

grant_type=refresh_token&
refresh_token={refresh_token}

Response:
{
  "access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "def50200...",
  "scope": "openid profile email"
}
```

#### 令牌撤销
```http
POST /oauth2/revoke
Content-Type: application/x-www-form-urlencoded
Authorization: Basic {base64(client_id:client_secret)}

token={access_token_or_refresh_token}&
token_type_hint=access_token

Response: 200 OK
```

### 2.2 用户信息端点

#### 获取用户信息
```http
GET /oauth2/userinfo
Authorization: Bearer {access_token}

Response:
{
  "sub": "user-uuid",
  "name": "张三",
  "given_name": "三",
  "family_name": "张",
  "email": "<EMAIL>",
  "email_verified": true,
  "phone_number": "+86-13800138000",
  "phone_number_verified": true,
  "picture": "https://example.com/avatar.jpg",
  "locale": "zh-CN",
  "tenant_id": "tenant-uuid",
  "roles": ["user", "admin"],
  "permissions": ["read", "write"]
}
```

### 2.3 JWKS端点

#### 获取公钥集合
```http
GET /.well-known/jwks.json

Response:
{
  "keys": [
    {
      "kty": "RSA",
      "use": "sig",
      "kid": "key-id",
      "n": "...",
      "e": "AQAB",
      "alg": "RS256"
    }
  ]
}
```

## 3. 用户管理API

### 3.1 用户注册（全局用户，无需租户信息）
```http
POST /api/v1/users/register
Content-Type: application/json

{
  "username": "zhangsan",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "name": "张三",
  "phone": "+86-13800138000"
}

Response: 201 Created
{
  "code": 201,
  "message": "User registered successfully",
  "data": {
    "id": "user-uuid",
    "username": "zhangsan",
    "email": "<EMAIL>",
    "name": "张三",
    "phone": "+86-13800138000",
    "status": "pending",
    "email_verified": false,
    "phone_verified": false,
    "tenant_count": 0,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3.2 邮箱验证
```http
POST /api/v1/users/verify-email
Content-Type: application/json

{
  "email": "<EMAIL>",
  "verification_code": "123456"
}

Response: 200 OK
{
  "code": 200,
  "message": "Email verified successfully",
  "data": {
    "email_verified": true
  }
}
```

### 3.3 获取用户资料
```http
GET /api/v1/users/profile
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "id": "user-uuid",
    "username": "zhangsan",
    "email": "<EMAIL>",
    "name": "张三",
    "phone": "+86-13800138000",
    "avatar_url": "https://example.com/avatar.jpg",
    "status": "active",
    "email_verified": true,
    "phone_verified": true,
    "mfa_enabled": false,
    "preferences": {
      "language": "zh-CN",
      "timezone": "Asia/Shanghai"
    },
    "last_login_at": "2024-01-01T11:00:00Z",
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T11:30:00Z"
  }
}
```

### 3.4 更新用户资料
```http
PUT /api/v1/users/profile
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "张三丰",
  "phone": "+86-13800138001",
  "avatar_url": "https://example.com/new-avatar.jpg",
  "preferences": {
    "language": "en-US",
    "timezone": "America/New_York"
  }
}

Response: 200 OK
{
  "code": 200,
  "message": "Profile updated successfully",
  "data": {
    "id": "user-uuid",
    "name": "张三丰",
    "phone": "+86-13800138001",
    "avatar_url": "https://example.com/new-avatar.jpg",
    "preferences": {
      "language": "en-US",
      "timezone": "America/New_York"
    },
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3.5 修改密码
```http
PUT /api/v1/users/password
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "current_password": "OldPassword123!",
  "new_password": "NewPassword456!",
  "confirm_password": "NewPassword456!"
}

Response: 200 OK
{
  "code": 200,
  "message": "Password updated successfully",
  "data": {
    "password_changed_at": "2024-01-01T12:00:00Z"
  }
}
```

### 3.6 获取用户租户列表（两级认证核心API）
```http
GET /api/v1/users/tenants
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "tenants": [
      {
        "id": "tenant-uuid-1",
        "name": "示例企业A",
        "domain": "company-a.example.com",
        "role": "admin",
        "status": "active",
        "is_default": true,
        "joined_at": "2024-01-01T10:00:00Z",
        "last_accessed_at": "2024-01-01T11:00:00Z"
      },
      {
        "id": "tenant-uuid-2",
        "name": "示例企业B",
        "domain": "company-b.example.com",
        "role": "user",
        "status": "active",
        "is_default": false,
        "joined_at": "2024-01-02T10:00:00Z",
        "last_accessed_at": "2024-01-02T15:00:00Z"
      }
    ],
    "total": 2,
    "current_tenant": {
      "id": "tenant-uuid-1",
      "name": "示例企业A"
    }
  }
}
```

### 3.7 切换租户上下文
```http
POST /api/v1/users/switch-tenant
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "tenant_id": "tenant-uuid-2"
}

Response: 200 OK
{
  "code": 200,
  "message": "Tenant switched successfully",
  "data": {
    "new_access_token": "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tenant_context": {
      "tenant_id": "tenant-uuid-2",
      "tenant_name": "示例企业B",
      "user_role": "user",
      "permissions": ["content:read"]
    },
    "switched_at": "2024-01-01T12:00:00Z"
  }
}
```

## 4. 多因子认证API

### 4.1 启用MFA
```http
POST /api/v1/users/mfa/enable
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "type": "totp" // totp, sms, email
}

Response: 200 OK
{
  "code": 200,
  "message": "MFA setup initiated",
  "data": {
    "secret": "JBSWY3DPEHPK3PXP",
    "qr_code": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
    "backup_codes": [
      "12345678",
      "87654321",
      "11111111",
      "22222222",
      "33333333"
    ]
  }
}
```

### 4.2 确认MFA启用
```http
POST /api/v1/users/mfa/confirm
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "verification_code": "123456"
}

Response: 200 OK
{
  "code": 200,
  "message": "MFA enabled successfully",
  "data": {
    "mfa_enabled": true,
    "enabled_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4.3 禁用MFA
```http
POST /api/v1/users/mfa/disable
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "verification_code": "123456",
  "password": "CurrentPassword123!"
}

Response: 200 OK
{
  "code": 200,
  "message": "MFA disabled successfully",
  "data": {
    "mfa_enabled": false,
    "disabled_at": "2024-01-01T12:00:00Z"
  }
}
```

### 4.4 生成新的备用码
```http
POST /api/v1/users/mfa/backup-codes/regenerate
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "verification_code": "123456"
}

Response: 200 OK
{
  "code": 200,
  "message": "Backup codes regenerated",
  "data": {
    "backup_codes": [
      "98765432",
      "12345678",
      "55555555",
      "66666666",
      "77777777"
    ]
  }
}
```

## 5. 租户管理API

### 5.1 创建租户
```http
POST /api/v1/tenants
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "示例企业",
  "domain": "example.com",
  "description": "这是一个示例企业",
  "tier": "standard",
  "settings": {
    "allow_registration": false,
    "require_email_verification": true,
    "session_timeout": 3600
  }
}

Response: 201 Created
{
  "code": 201,
  "message": "Tenant created successfully",
  "data": {
    "id": "tenant-uuid",
    "name": "示例企业",
    "domain": "example.com",
    "description": "这是一个示例企业",
    "tier": "standard",
    "status": "active",
    "settings": {
      "allow_registration": false,
      "require_email_verification": true,
      "session_timeout": 3600
    },
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5.2 获取租户列表
```http
GET /api/v1/tenants?page=1&size=20&status=active
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "tenant-uuid",
        "name": "示例企业",
        "domain": "example.com",
        "status": "active",
        "tier": "standard",
        "user_count": 150,
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 5.3 邀请用户加入租户
```http
POST /api/v1/tenants/{tenant_id}/invitations
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "role": "user",
  "message": "欢迎加入我们的团队！",
  "expires_in": 7
}

Response: 201 Created
{
  "code": 201,
  "message": "Invitation sent successfully",
  "data": {
    "id": "invitation-uuid",
    "email": "<EMAIL>",
    "role": "user",
    "status": "pending",
    "expires_at": "2024-01-08T12:00:00Z",
    "invitation_url": "https://auth.example.com/invitations/accept?token=abc123",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5.4 获取租户邀请列表
```http
GET /api/v1/tenants/{tenant_id}/invitations?status=pending&page=1&size=20
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "invitation-uuid",
        "email": "<EMAIL>",
        "role": "user",
        "status": "pending",
        "invited_by": "张三",
        "expires_at": "2024-01-08T12:00:00Z",
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 5.5 用户确认邀请
```http
PUT /api/v1/invitations/{invitation_id}/confirm
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "action": "accept" // accept | reject
}

Response: 200 OK
{
  "code": 200,
  "message": "Invitation accepted successfully",
  "data": {
    "status": "accepted",
    "tenant": {
      "id": "tenant-uuid",
      "name": "示例企业",
      "domain": "example.com"
    },
    "role": "user",
    "accepted_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5.6 用户申请加入租户
```http
POST /api/v1/tenants/{tenant_id}/applications
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "message": "我希望加入贵公司的团队",
  "expected_role": "user"
}

Response: 201 Created
{
  "code": 201,
  "message": "Application submitted successfully",
  "data": {
    "id": "application-uuid",
    "tenant_id": "tenant-uuid",
    "message": "我希望加入贵公司的团队",
    "expected_role": "user",
    "status": "pending",
    "expires_at": "2024-01-08T12:00:00Z",
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 5.7 处理用户申请
```http
PUT /api/v1/tenants/{tenant_id}/applications/{application_id}
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "action": "approve", // approve | reject
  "role": "user",
  "message": "欢迎加入我们的团队"
}

Response: 200 OK
{
  "code": 200,
  "message": "Application processed successfully",
  "data": {
    "status": "approved",
    "role": "user",
    "processed_at": "2024-01-01T12:00:00Z",
    "process_message": "欢迎加入我们的团队"
  }
}
```

## 6. 权限管理API

### 6.1 获取角色列表
```http
GET /api/v1/tenants/{tenant_id}/roles?page=1&size=20
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "role-uuid",
        "name": "admin",
        "description": "管理员角色",
        "permissions": {
          "user:read": true,
          "user:write": true,
          "tenant:manage": true
        },
        "is_system": false,
        "is_default": false,
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

### 6.2 创建角色
```http
POST /api/v1/tenants/{tenant_id}/roles
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "name": "editor",
  "description": "编辑者角色",
  "permissions": {
    "content:read": true,
    "content:write": true,
    "content:delete": false
  }
}

Response: 201 Created
{
  "code": 201,
  "message": "Role created successfully",
  "data": {
    "id": "role-uuid",
    "name": "editor",
    "description": "编辑者角色",
    "permissions": {
      "content:read": true,
      "content:write": true,
      "content:delete": false
    },
    "is_system": false,
    "is_default": false,
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 6.3 分配用户角色
```http
PUT /api/v1/tenants/{tenant_id}/users/{user_id}/role
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "role": "editor",
  "permissions": {
    "special:access": true
  }
}

Response: 200 OK
{
  "code": 200,
  "message": "User role updated successfully",
  "data": {
    "user_id": "user-uuid",
    "tenant_id": "tenant-uuid",
    "role": "editor",
    "permissions": {
      "special:access": true
    },
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

### 6.4 检查用户权限
```http
POST /api/v1/permissions/check
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "user_id": "user-uuid",
  "tenant_id": "tenant-uuid",
  "permissions": ["content:read", "content:write", "admin:access"]
}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "permissions": {
      "content:read": true,
      "content:write": true,
      "admin:access": false
    }
  }
}
```

## 7. OAuth客户端管理API

### 7.1 注册OAuth客户端
```http
POST /api/v1/tenants/{tenant_id}/oauth-clients
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "client_name": "Web应用",
  "client_description": "公司官网应用",
  "client_type": "confidential",
  "grant_types": ["authorization_code", "refresh_token"],
  "redirect_uris": ["https://example.com/callback"],
  "scopes": ["openid", "profile", "email"],
  "access_token_validity": 3600,
  "refresh_token_validity": 604800
}

Response: 201 Created
{
  "code": 201,
  "message": "OAuth client created successfully",
  "data": {
    "id": "client-uuid",
    "client_id": "web_app_12345",
    "client_secret": "secret_67890",
    "client_name": "Web应用",
    "client_type": "confidential",
    "grant_types": ["authorization_code", "refresh_token"],
    "redirect_uris": ["https://example.com/callback"],
    "scopes": ["openid", "profile", "email"],
    "created_at": "2024-01-01T12:00:00Z"
  }
}
```

### 7.2 获取OAuth客户端列表
```http
GET /api/v1/tenants/{tenant_id}/oauth-clients?page=1&size=20
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "client-uuid",
        "client_id": "web_app_12345",
        "client_name": "Web应用",
        "client_type": "confidential",
        "scopes": ["openid", "profile", "email"],
        "created_at": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 20,
      "total": 1,
      "total_pages": 1
    }
  }
}
```

## 8. 审计日志API

### 8.1 获取审计日志
```http
GET /api/v1/audit-logs?
    tenant_id={tenant_id}&
    user_id={user_id}&
    action={action}&
    start_date=2024-01-01&
    end_date=2024-01-31&
    page=1&
    size=50
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "log-uuid",
        "tenant_id": "tenant-uuid",
        "user_id": "user-uuid",
        "action": "user.login",
        "resource_type": "user",
        "resource_id": "user-uuid",
        "details": {
          "ip_address": "*************",
          "user_agent": "Mozilla/5.0...",
          "success": true
        },
        "timestamp": "2024-01-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "size": 50,
      "total": 100,
      "total_pages": 2
    }
  }
}
```

## 9. 系统配置API

### 9.1 获取系统配置
```http
GET /api/v1/tenants/{tenant_id}/configs?category=security
Authorization: Bearer {access_token}

Response: 200 OK
{
  "code": 200,
  "message": "Success",
  "data": {
    "configs": {
      "password_policy": {
        "min_length": 8,
        "require_uppercase": true,
        "require_lowercase": true,
        "require_numbers": true,
        "require_symbols": true
      },
      "session_timeout": 3600,
      "max_login_attempts": 5,
      "lockout_duration": 900
    }
  }
}
```

### 9.2 更新系统配置
```http
PUT /api/v1/tenants/{tenant_id}/configs
Authorization: Bearer {access_token}
Content-Type: application/json

{
  "category": "security",
  "configs": {
    "password_policy": {
      "min_length": 10,
      "require_uppercase": true,
      "require_lowercase": true,
      "require_numbers": true,
      "require_symbols": true
    },
    "session_timeout": 7200
  }
}

Response: 200 OK
{
  "code": 200,
  "message": "Configuration updated successfully",
  "data": {
    "updated_configs": [
      "password_policy",
      "session_timeout"
    ],
    "updated_at": "2024-01-01T12:00:00Z"
  }
}
```

## 10. 错误处理和状态码

### 10.1 常见错误响应
```json
// 参数验证错误
{
  "code": 400,
  "message": "Validation failed",
  "errors": [
    {
      "field": "email",
      "message": "Invalid email format",
      "code": "INVALID_FORMAT"
    },
    {
      "field": "password",
      "message": "Password too weak",
      "code": "WEAK_PASSWORD"
    }
  ],
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}

// 认证错误
{
  "code": 401,
  "message": "Authentication required",
  "error": "invalid_token",
  "error_description": "The access token is invalid or expired",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}

// 权限错误
{
  "code": 403,
  "message": "Insufficient permissions",
  "error": "access_denied",
  "error_description": "You don't have permission to access this resource",
  "required_permissions": ["admin:read"],
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}

// 资源冲突
{
  "code": 409,
  "message": "Resource conflict",
  "error": "duplicate_resource",
  "error_description": "A user with this email already exists",
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}

// 频率限制
{
  "code": 429,
  "message": "Too many requests",
  "error": "rate_limit_exceeded",
  "error_description": "API rate limit exceeded",
  "retry_after": 60,
  "timestamp": "2024-01-01T12:00:00Z",
  "request_id": "req-uuid"
}
```

## 11. API版本控制和兼容性

### 11.1 版本控制策略
- **URL版本控制**: `/api/v1/`, `/api/v2/`
- **向后兼容**: 新版本保持向后兼容性
- **废弃通知**: 通过响应头通知API废弃信息
- **迁移指南**: 提供详细的版本迁移文档

### 11.2 版本废弃响应头
```http
HTTP/1.1 200 OK
X-API-Version: v1
X-API-Deprecated: true
X-API-Sunset: 2024-12-31T23:59:59Z
X-API-Migration-Guide: https://docs.example.com/api/v1-to-v2
```

这个API设计规范为统一认证授权系统提供了完整的接口定义，遵循RESTful设计原则，支持OAuth 2.0标准协议，确保系统的互操作性和可扩展性。涵盖了认证授权、用户管理、租户管理、权限控制、OAuth客户端管理、审计日志等所有核心功能的API接口。
