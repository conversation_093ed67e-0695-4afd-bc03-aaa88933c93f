# 统一认证授权系统 - 架构设计文档

## 📋 文档目录

| 编号 | 文档名称 | 描述 | 状态 |
|------|----------|------|------|
| 00 | [项目架构总览](./00-项目架构总览.md) | 整个项目的架构总览和文档导航 | ✅ 已完成 |
| 01 | [PRD需求文档](./01-PRD-统一认证授权系统.md) | 产品需求文档，定义两级认证体系 | ✅ 已完成 |
| 02 | [系统架构设计](./02-系统架构设计.md) | 整体架构设计，包含两级认证流程 | ✅ 已修正 |
| 03 | [技术架构详细设计](./03-技术架构详细设计.md) | 技术实现方案和代码示例 | ✅ 已修正 |
| 04 | [数据库设计](./04-数据库设计-PostgreSQL17.md) | PostgreSQL17继承表分表设计 | ✅ 已修正 |
| 05 | [API设计规范](./05-API设计规范.md) | 支持两级认证的RESTful API | ✅ 已修正 |
| 06 | [部署架构设计](./06-部署架构设计.md) | 生产环境部署方案 | ✅ 已完成 |
| 07 | [安全架构设计](./07-安全架构设计.md) | 安全防护方案 | ✅ 已完成 |

## 🔑 核心架构特点

### 两级认证体系
根据PRD需求，系统采用**两级认证体系**：

1. **第一级：用户认证**
   - 用户在系统中全局注册，拥有唯一身份
   - 用户可以独立存在，不依赖于任何租户
   - 支持多因子认证(MFA)

2. **第二级：租户认证**
   - 用户可以加入多个租户
   - 在每个租户中有不同的角色和权限
   - 登录后需要选择要进入的租户（如果用户属于多个租户）

### 关键设计修正

#### ❌ 原设计问题
- 用户表包含tenant_id字段，用户必须属于某个租户
- 认证流程中用户和租户绑定
- 不支持用户在多个租户间自由切换

#### ✅ 修正后设计
- 用户表去掉tenant_id，用户是全局的
- 通过user_tenants关系表管理用户与租户的多对多关系
- 支持用户在多个租户间自由切换
- 认证流程分为用户认证和租户选择两个阶段

## 🏗️ 架构核心组件

### 数据库设计
```sql
-- 全局用户表（无租户信息）
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    -- 其他用户属性
);

-- 用户租户关系表（核心）
CREATE TABLE user_tenants (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    tenant_id UUID REFERENCES tenants(id),
    role VARCHAR(50) NOT NULL,
    status user_tenant_status_enum DEFAULT 'pending',
    is_default BOOLEAN DEFAULT FALSE,
    -- 其他关系属性
    UNIQUE(user_id, tenant_id)
);
```

## 🔧 技术栈

- **后端**: Spring Boot 3.x + Spring Security 6.x + Spring Authorization Server 1.x
- **数据库**: PostgreSQL 17 + Redis 7.0
- **前端**: React 18 + TypeScript + Ant Design
- **部署**: Docker + Kubernetes
- **监控**: Prometheus + Grafana + ELK Stack

---

*本文档基于PRD需求重新设计，采用两级认证体系，确保架构设计与业务需求完全匹配。*文档

## 📁 文档目录结构

```
doc/
├── README.md                           # 文档说明
└── PRD-统一认证授权系统.md              # 产品需求文档
```

## 📋 文档说明

### PRD-统一认证授权系统.md
这是统一认证授权系统的完整产品需求文档（Product Requirements Document），包含：

#### 🏗️ 系统设计
- **第1章**: 产品概述 - 产品背景、定位和核心价值
- **第2章**: 系统架构图 - 整体架构设计和数据流图
- **第3章**: 核心业务流程图 - 关键业务流程的Mermaid图表

#### 👥 角色与功能
- **第4章**: 用户角色定义 - 系统管理员、租户管理员、普通用户
- **第5章**: 核心功能需求 - 用户管理、租户管理、权限控制、SSO

#### 🔧 技术规范
- **第6章**: 技术架构需求 - 系统架构、数据存储、安全要求
- **第7章**: 非功能性需求 - 性能、扩展性、合规要求
- **第8章**: 用户体验设计 - 界面设计和用户流程

#### 📈 项目管理
- **第9章**: 实施计划 - 分阶段开发路线图
- **第10章**: 风险评估 - 技术、安全、业务风险
- **第11章**: 成功指标 - 技术和业务指标

#### 📖 详细规范
- **第12章**: 详细业务流程 - 用户生命周期、权限模型、安全策略
- **第13章**: API接口规范 - 认证、用户管理、租户管理接口
- **第14章**: 数据库设计 - 核心表结构设计
- **第15章**: 附录 - 术语表和参考标准

## 🎯 核心特性

### 🔐 安全认证
- **多因子认证(MFA)**: 支持TOTP、SMS、邮箱验证
- **单点登录(SSO)**: 支持SAML、OAuth 2.0、OpenID Connect
- **密码策略**: 强密码要求、历史密码检查、定期更新

### 🏢 多租户架构
- **两级账户体系**: 用户可加入多个租户
- **租户隔离**: 数据和权限完全隔离
- **多种加入方式**:
  - 用户自主创建租户（自动成为管理员）
  - 管理员邀请用户（支持邀请任何邮箱，无论是否已注册）
  - 用户申请加入（需管理员审批）
  - 开放注册（可选功能）
- **灵活邀请机制**:
  - 可邀请任何邮箱地址，无论用户是否已在系统中注册
  - 未注册用户通过邀请链接引导完成注册
  - 已注册用户直接显示邀请详情
  - 用户必须确认才能加入租户

### 🛡️ 权限管理
- **基于角色的访问控制(RBAC)**: 细粒度权限控制
- **动态权限验证**: 实时权限检查和缓存机制
- **审计日志**: 完整的操作审计和安全监控

### 🔄 业务流程
文档包含完整的Mermaid流程图：
- 用户注册与租户加入流程
- 用户登录与MFA认证流程
- 租户管理员邀请用户流程（支持邀请任何邮箱）
- 用户申请加入租户流程
- 用户创建租户流程
- 用户确认邀请流程
- 邀请任何邮箱用户的时序图
- 完整的租户加入方式总览
- 单点登录(SSO)认证流程
- 权限验证与授权流程

### 🏗️ 系统架构
- **微服务架构**: 高可用、可扩展的分布式设计
- **API优先**: RESTful API设计，易于集成
- **标准协议**: 支持主流认证授权协议
- **云原生**: 容器化部署，支持Kubernetes

## 🚀 快速开始

1. **阅读产品概述**: 了解系统背景和核心价值
2. **查看架构图**: 理解系统整体设计
3. **研究业务流程**: 掌握关键业务流程
4. **参考技术规范**: 了解技术实现要求
5. **制定实施计划**: 根据分阶段计划开始开发

## 📞 联系方式

如有任何问题或建议，请联系产品团队。

---

*本文档使用Markdown格式编写，包含Mermaid图表，建议使用支持Mermaid渲染的工具查看。*
