# 06-统一认证授权系统 - 部署架构设计

## 1. 部署架构概述

### 1.1 部署策略
- **容器化部署**: 使用Docker容器化所有服务组件
- **微服务架构**: 服务独立部署，支持水平扩展
- **高可用设计**: 多实例部署，消除单点故障
- **负载均衡**: 多层负载均衡，确保流量分发
- **自动化运维**: CI/CD流水线，自动化部署和监控

### 1.2 环境规划
```
开发环境 (Development)
├── 单机部署
├── Docker Compose
└── 本地数据库

测试环境 (Testing)
├── 多服务实例
├── Kubernetes集群
└── 独立数据库

预生产环境 (Staging)
├── 生产级配置
├── 完整监控
└── 性能测试

生产环境 (Production)
├── 高可用集群
├── 多数据中心
└── 完整备份
```

## 2. 容器化架构

### 2.1 Docker镜像设计

#### 基础镜像Dockerfile
```dockerfile
# 认证服务镜像
FROM openjdk:17-jdk-slim as builder
WORKDIR /app
COPY pom.xml .
COPY src ./src
RUN ./mvnw clean package -DskipTests

FROM openjdk:17-jre-slim
WORKDIR /app
COPY --from=builder /app/target/auth-service.jar app.jar
EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1
ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 前端应用镜像
```dockerfile
# React前端镜像
FROM node:18-alpine as builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
HEALTHCHECK --interval=30s --timeout=3s --start-period=10s --retries=3 \
  CMD curl -f http://localhost/health || exit 1
```

### 2.2 Docker Compose开发环境
```yaml
version: '3.8'

services:
  # 认证服务
  auth-service:
    build: ./auth-service
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=***************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 用户管理服务
  user-service:
    build: ./user-service
    ports:
      - "8081:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=***************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  # 租户管理服务
  tenant-service:
    build: ./tenant-service
    ports:
      - "8082:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=***************************************
    depends_on:
      - postgres

  # 权限管理服务
  permission-service:
    build: ./permission-service
    ports:
      - "8083:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DATABASE_URL=***************************************
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis

  # 通知服务
  notification-service:
    build: ./notification-service
    ports:
      - "8084:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - RABBITMQ_URL=amqp://rabbitmq:5672
    depends_on:
      - rabbitmq

  # 前端应用
  web-app:
    build: ./web-app
    ports:
      - "3000:80"
    depends_on:
      - auth-service

  # API网关
  api-gateway:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - auth-service
      - user-service
      - tenant-service
      - permission-service

  # PostgreSQL数据库
  postgres:
    image: postgres:17
    environment:
      - POSTGRES_DB=auth_db
      - POSTGRES_USER=auth_user
      - POSTGRES_PASSWORD=auth_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Elasticsearch
  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
    ports:
      - "9200:9200"
    volumes:
      - es_data:/usr/share/elasticsearch/data

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
  es_data:
```

## 3. Kubernetes生产部署

### 3.1 Kubernetes集群架构
```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "Ingress层"
            INGRESS[Nginx Ingress Controller]
            CERT[Cert-Manager]
        end
        
        subgraph "应用层"
            subgraph "认证服务"
                AUTH1[auth-service-1]
                AUTH2[auth-service-2]
                AUTH3[auth-service-3]
            end
            
            subgraph "用户服务"
                USER1[user-service-1]
                USER2[user-service-2]
            end
            
            subgraph "租户服务"
                TENANT1[tenant-service-1]
                TENANT2[tenant-service-2]
            end
            
            subgraph "权限服务"
                PERM1[permission-service-1]
                PERM2[permission-service-2]
            end
        end
        
        subgraph "数据层"
            subgraph "PostgreSQL集群"
                PG_MASTER[PostgreSQL Master]
                PG_SLAVE1[PostgreSQL Slave 1]
                PG_SLAVE2[PostgreSQL Slave 2]
            end
            
            subgraph "Redis集群"
                REDIS_MASTER[Redis Master]
                REDIS_SLAVE1[Redis Slave 1]
                REDIS_SLAVE2[Redis Slave 2]
            end
        end
        
        subgraph "存储层"
            PVC1[PVC - PostgreSQL]
            PVC2[PVC - Redis]
            PVC3[PVC - Logs]
        end
    end

    INGRESS --> AUTH1
    INGRESS --> AUTH2
    INGRESS --> AUTH3
    INGRESS --> USER1
    INGRESS --> USER2
    INGRESS --> TENANT1
    INGRESS --> TENANT2
    INGRESS --> PERM1
    INGRESS --> PERM2
    
    AUTH1 --> PG_MASTER
    AUTH2 --> PG_MASTER
    AUTH3 --> PG_MASTER
    USER1 --> PG_SLAVE1
    USER2 --> PG_SLAVE2
    
    AUTH1 --> REDIS_MASTER
    AUTH2 --> REDIS_MASTER
    PERM1 --> REDIS_MASTER
    PERM2 --> REDIS_MASTER
    
    PG_MASTER --> PVC1
    REDIS_MASTER --> PVC2
```

### 3.2 Kubernetes部署清单

#### Namespace
```yaml
apiVersion: v1
kind: Namespace
metadata:
  name: auth-system
  labels:
    name: auth-system
```

#### ConfigMap
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: auth-config
  namespace: auth-system
data:
  application.yml: |
    spring:
      profiles:
        active: kubernetes
      datasource:
        url: ***********************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      redis:
        host: redis-service
        port: 6379
    logging:
      level:
        com.company.auth: DEBUG
    management:
      endpoints:
        web:
          exposure:
            include: health,info,metrics,prometheus
```

#### Secret
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: auth-secrets
  namespace: auth-system
type: Opaque
data:
  db-username: YXV0aF91c2Vy  # base64 encoded
  db-password: YXV0aF9wYXNzd29yZA==  # base64 encoded
  jwt-secret: c3VwZXJfc2VjcmV0X2tleQ==  # base64 encoded
```

#### Deployment - 认证服务
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: auth-service
  namespace: auth-system
  labels:
    app: auth-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: auth-service
  template:
    metadata:
      labels:
        app: auth-service
    spec:
      containers:
      - name: auth-service
        image: auth-service:latest
        ports:
        - containerPort: 8080
        env:
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: db-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: auth-secrets
              key: jwt-secret
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: auth-config
```

#### Service
```yaml
apiVersion: v1
kind: Service
metadata:
  name: auth-service
  namespace: auth-system
  labels:
    app: auth-service
spec:
  selector:
    app: auth-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

#### HorizontalPodAutoscaler
```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: auth-service-hpa
  namespace: auth-system
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: auth-service
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

#### Ingress
```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-ingress
  namespace: auth-system
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
spec:
  tls:
  - hosts:
    - auth.example.com
    secretName: auth-tls
  rules:
  - host: auth.example.com
    http:
      paths:
      - path: /oauth2
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 80
      - path: /api/v1/users
        pathType: Prefix
        backend:
          service:
            name: user-service
            port:
              number: 80
      - path: /api/v1/tenants
        pathType: Prefix
        backend:
          service:
            name: tenant-service
            port:
              number: 80
```

## 4. 数据库高可用部署

### 4.1 PostgreSQL主从复制
```yaml
# PostgreSQL Master
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgres-master
  namespace: auth-system
spec:
  serviceName: postgres-master
  replicas: 1
  selector:
    matchLabels:
      app: postgres-master
  template:
    metadata:
      labels:
        app: postgres-master
    spec:
      containers:
      - name: postgres
        image: postgres:17
        env:
        - name: POSTGRES_DB
          value: auth_db
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: password
        - name: POSTGRES_REPLICATION_USER
          value: replicator
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secrets
              key: replication-password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: postgres-config
        configMap:
          name: postgres-config
  volumeClaimTemplates:
  - metadata:
      name: postgres-storage
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 100Gi
      storageClassName: fast-ssd
```

### 4.2 Redis集群部署
```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: auth-system
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis-cluster
  template:
    metadata:
      labels:
        app: redis-cluster
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        - containerPort: 16379
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
      storageClassName: fast-ssd
```

## 5. 监控和日志系统

### 5.1 Prometheus监控部署
```yaml
# Prometheus配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: monitoring
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
      evaluation_interval: 15s

    rule_files:
      - "/etc/prometheus/rules/*.yml"

    scrape_configs:
      - job_name: 'kubernetes-pods'
        kubernetes_sd_configs:
        - role: pod
        relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__

      - job_name: 'auth-services'
        static_configs:
        - targets: ['auth-service:80', 'user-service:80', 'tenant-service:80']
        metrics_path: /actuator/prometheus
        scrape_interval: 10s

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:latest
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config-volume
          mountPath: /etc/prometheus
        - name: storage-volume
          mountPath: /prometheus
        command:
        - '/bin/prometheus'
        - '--config.file=/etc/prometheus/prometheus.yml'
        - '--storage.tsdb.path=/prometheus'
        - '--web.console.libraries=/etc/prometheus/console_libraries'
        - '--web.console.templates=/etc/prometheus/consoles'
        - '--storage.tsdb.retention.time=30d'
        - '--web.enable-lifecycle'
      volumes:
      - name: config-volume
        configMap:
          name: prometheus-config
      - name: storage-volume
        persistentVolumeClaim:
          claimName: prometheus-pvc
```

### 5.2 Grafana仪表板部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: grafana
  template:
    metadata:
      labels:
        app: grafana
    spec:
      containers:
      - name: grafana
        image: grafana/grafana:latest
        ports:
        - containerPort: 3000
        env:
        - name: GF_SECURITY_ADMIN_PASSWORD
          valueFrom:
            secretKeyRef:
              name: grafana-secrets
              key: admin-password
        - name: GF_INSTALL_PLUGINS
          value: "grafana-piechart-panel,grafana-worldmap-panel"
        volumeMounts:
        - name: grafana-storage
          mountPath: /var/lib/grafana
        - name: grafana-config
          mountPath: /etc/grafana/provisioning
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: grafana-storage
        persistentVolumeClaim:
          claimName: grafana-pvc
      - name: grafana-config
        configMap:
          name: grafana-config
```

### 5.3 ELK日志系统部署
```yaml
# Elasticsearch
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: elasticsearch
  namespace: logging
spec:
  serviceName: elasticsearch
  replicas: 3
  selector:
    matchLabels:
      app: elasticsearch
  template:
    metadata:
      labels:
        app: elasticsearch
    spec:
      containers:
      - name: elasticsearch
        image: elasticsearch:8.11.0
        ports:
        - containerPort: 9200
        - containerPort: 9300
        env:
        - name: cluster.name
          value: "auth-logs"
        - name: node.name
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: discovery.seed_hosts
          value: "elasticsearch-0.elasticsearch,elasticsearch-1.elasticsearch,elasticsearch-2.elasticsearch"
        - name: cluster.initial_master_nodes
          value: "elasticsearch-0,elasticsearch-1,elasticsearch-2"
        - name: ES_JAVA_OPTS
          value: "-Xms1g -Xmx1g"
        - name: xpack.security.enabled
          value: "false"
        volumeMounts:
        - name: es-data
          mountPath: /usr/share/elasticsearch/data
        resources:
          requests:
            memory: "2Gi"
            cpu: "500m"
          limits:
            memory: "4Gi"
            cpu: "1000m"
  volumeClaimTemplates:
  - metadata:
      name: es-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 50Gi
      storageClassName: fast-ssd

---
# Logstash
apiVersion: apps/v1
kind: Deployment
metadata:
  name: logstash
  namespace: logging
spec:
  replicas: 2
  selector:
    matchLabels:
      app: logstash
  template:
    metadata:
      labels:
        app: logstash
    spec:
      containers:
      - name: logstash
        image: logstash:8.11.0
        ports:
        - containerPort: 5044
        - containerPort: 9600
        volumeMounts:
        - name: logstash-config
          mountPath: /usr/share/logstash/pipeline
        - name: logstash-settings
          mountPath: /usr/share/logstash/config
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: logstash-config
        configMap:
          name: logstash-config
      - name: logstash-settings
        configMap:
          name: logstash-settings

---
# Kibana
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kibana
  namespace: logging
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kibana
  template:
    metadata:
      labels:
        app: kibana
    spec:
      containers:
      - name: kibana
        image: kibana:8.11.0
        ports:
        - containerPort: 5601
        env:
        - name: ELASTICSEARCH_HOSTS
          value: "http://elasticsearch:9200"
        - name: SERVER_NAME
          value: "kibana"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "500m"
```

## 6. CI/CD流水线

### 6.1 GitLab CI配置
```yaml
# .gitlab-ci.yml
stages:
  - test
  - build
  - security-scan
  - deploy-dev
  - deploy-staging
  - deploy-prod

variables:
  DOCKER_REGISTRY: registry.example.com
  KUBERNETES_NAMESPACE_DEV: auth-system-dev
  KUBERNETES_NAMESPACE_STAGING: auth-system-staging
  KUBERNETES_NAMESPACE_PROD: auth-system

# 单元测试
test:
  stage: test
  image: openjdk:17-jdk-slim
  script:
    - ./mvnw clean test
    - ./mvnw jacoco:report
  artifacts:
    reports:
      junit: target/surefire-reports/TEST-*.xml
      coverage: target/site/jacoco/jacoco.xml
  coverage: '/Total.*?([0-9]{1,3})%/'

# 构建Docker镜像
build:
  stage: build
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA .
    - docker push $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA
    - docker tag $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA $DOCKER_REGISTRY/auth-service:latest
    - docker push $DOCKER_REGISTRY/auth-service:latest
  only:
    - main
    - develop

# 安全扫描
security-scan:
  stage: security-scan
  image: aquasec/trivy:latest
  script:
    - trivy image --exit-code 0 --severity HIGH,CRITICAL $DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA
  allow_failure: true
  only:
    - main
    - develop

# 部署到开发环境
deploy-dev:
  stage: deploy-dev
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_DEV
    - kubectl set image deployment/auth-service auth-service=$DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_DEV
    - kubectl rollout status deployment/auth-service -n $KUBERNETES_NAMESPACE_DEV
  environment:
    name: development
    url: https://auth-dev.example.com
  only:
    - develop

# 部署到预生产环境
deploy-staging:
  stage: deploy-staging
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_STAGING
    - kubectl set image deployment/auth-service auth-service=$DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_STAGING
    - kubectl rollout status deployment/auth-service -n $KUBERNETES_NAMESPACE_STAGING
  environment:
    name: staging
    url: https://auth-staging.example.com
  when: manual
  only:
    - main

# 部署到生产环境
deploy-prod:
  stage: deploy-prod
  image: bitnami/kubectl:latest
  script:
    - kubectl config use-context $KUBE_CONTEXT_PROD
    - kubectl set image deployment/auth-service auth-service=$DOCKER_REGISTRY/auth-service:$CI_COMMIT_SHA -n $KUBERNETES_NAMESPACE_PROD
    - kubectl rollout status deployment/auth-service -n $KUBERNETES_NAMESPACE_PROD
  environment:
    name: production
    url: https://auth.example.com
  when: manual
  only:
    - main
```

### 6.2 Helm Chart部署
```yaml
# Chart.yaml
apiVersion: v2
name: auth-system
description: 统一认证授权系统
type: application
version: 1.0.0
appVersion: "1.0.0"

---
# values.yaml
global:
  imageRegistry: registry.example.com
  imagePullSecrets:
    - name: registry-secret

authService:
  replicaCount: 3
  image:
    repository: auth-service
    tag: latest
    pullPolicy: IfNotPresent

  service:
    type: ClusterIP
    port: 80
    targetPort: 8080

  resources:
    requests:
      memory: "512Mi"
      cpu: "250m"
    limits:
      memory: "1Gi"
      cpu: "500m"

  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

postgresql:
  enabled: true
  auth:
    postgresPassword: "auth_password"
    username: "auth_user"
    password: "auth_password"
    database: "auth_db"

  primary:
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"

  readReplicas:
    replicaCount: 2
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"

redis:
  enabled: true
  auth:
    enabled: false

  master:
    persistence:
      enabled: true
      size: 10Gi
      storageClass: "fast-ssd"

  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 10Gi
      storageClass: "fast-ssd"

ingress:
  enabled: true
  className: "nginx"
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/rate-limit: "100"
  hosts:
    - host: auth.example.com
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: auth-tls
      hosts:
        - auth.example.com
```

## 7. 备份和灾难恢复

### 7.1 数据库备份策略
```bash
#!/bin/bash
# 数据库备份脚本
BACKUP_DIR="/backup/postgresql"
DATE=$(date +%Y%m%d_%H%M%S)
NAMESPACE="auth-system"
POD_NAME=$(kubectl get pods -n $NAMESPACE -l app=postgres-master -o jsonpath='{.items[0].metadata.name}')

# 创建备份目录
mkdir -p $BACKUP_DIR

# 全量备份
kubectl exec -n $NAMESPACE $POD_NAME -- pg_dump -U auth_user -d auth_db --format=custom --compress=9 > $BACKUP_DIR/auth_db_full_$DATE.dump

# 上传到对象存储
aws s3 cp $BACKUP_DIR/auth_db_full_$DATE.dump s3://backup-bucket/postgresql/

# 清理本地备份文件（保留7天）
find $BACKUP_DIR -name "*.dump" -mtime +7 -delete

# 备份验证
kubectl exec -n $NAMESPACE $POD_NAME -- pg_dump -U auth_user -d auth_db --schema-only > $BACKUP_DIR/schema_$DATE.sql
```

### 7.2 Kubernetes资源备份
```bash
#!/bin/bash
# Kubernetes资源备份脚本
BACKUP_DIR="/backup/kubernetes"
DATE=$(date +%Y%m%d_%H%M%S)
NAMESPACE="auth-system"

mkdir -p $BACKUP_DIR/$DATE

# 备份所有资源
kubectl get all,configmap,secret,pvc,ingress -n $NAMESPACE -o yaml > $BACKUP_DIR/$DATE/all-resources.yaml

# 备份特定资源
kubectl get deployment -n $NAMESPACE -o yaml > $BACKUP_DIR/$DATE/deployments.yaml
kubectl get service -n $NAMESPACE -o yaml > $BACKUP_DIR/$DATE/services.yaml
kubectl get configmap -n $NAMESPACE -o yaml > $BACKUP_DIR/$DATE/configmaps.yaml

# 压缩备份文件
tar -czf $BACKUP_DIR/k8s_backup_$DATE.tar.gz -C $BACKUP_DIR $DATE

# 上传到对象存储
aws s3 cp $BACKUP_DIR/k8s_backup_$DATE.tar.gz s3://backup-bucket/kubernetes/

# 清理本地备份
rm -rf $BACKUP_DIR/$DATE
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

### 7.3 灾难恢复计划
```yaml
# 灾难恢复流程
apiVersion: v1
kind: ConfigMap
metadata:
  name: disaster-recovery-plan
  namespace: auth-system
data:
  recovery-steps.md: |
    # 灾难恢复步骤

    ## 1. 评估损坏程度
    - 检查集群状态
    - 确认数据损坏范围
    - 评估恢复时间目标(RTO)和恢复点目标(RPO)

    ## 2. 数据库恢复
    ```bash
    # 从备份恢复PostgreSQL
    kubectl exec -n auth-system postgres-master-0 -- pg_restore -U auth_user -d auth_db /backup/auth_db_full_latest.dump
    ```

    ## 3. 应用服务恢复
    ```bash
    # 重新部署应用服务
    helm upgrade auth-system ./helm/auth-system -n auth-system
    ```

    ## 4. 验证恢复
    - 检查所有Pod状态
    - 验证数据库连接
    - 执行健康检查
    - 进行功能测试

    ## 5. 切换流量
    - 更新DNS记录
    - 验证负载均衡
    - 监控系统指标
```

这个部署架构设计为统一认证授权系统提供了完整的生产级部署方案，包括容器化、Kubernetes集群部署、高可用数据库配置、监控日志系统、CI/CD流水线和灾难恢复计划，确保系统的稳定性、可扩展性和高可用性。
