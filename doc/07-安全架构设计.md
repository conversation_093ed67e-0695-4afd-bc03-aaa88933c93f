# 07-统一认证授权系统 - 安全架构设计

## 1. 安全架构概述

### 1.1 安全设计原则
- **纵深防御**: 多层安全防护，确保单点失效不影响整体安全
- **最小权限**: 用户和服务只获得完成任务所需的最小权限
- **零信任**: 不信任任何网络位置，所有访问都需要验证
- **数据保护**: 静态和传输中的数据都需要加密保护
- **审计追踪**: 所有安全相关操作都需要记录和审计

### 1.2 安全威胁模型
```mermaid
graph TB
    subgraph "外部威胁"
        DDOS[DDoS攻击]
        BRUTE[暴力破解]
        PHISH[钓鱼攻击]
        SOCIAL[社会工程]
    end

    subgraph "应用层威胁"
        INJECTION[注入攻击]
        XSS[跨站脚本]
        CSRF[跨站请求伪造]
        AUTH_BYPASS[认证绕过]
    end

    subgraph "数据层威胁"
        DATA_LEAK[数据泄露]
        PRIV_ESC[权限提升]
        INSIDER[内部威胁]
        BACKUP_THEFT[备份盗取]
    end

    subgraph "基础设施威胁"
        CONTAINER_ESC[容器逃逸]
        K8S_MISC[K8s配置错误]
        NETWORK_SNIFF[网络嗅探]
        SUPPLY_CHAIN[供应链攻击]
    end

    subgraph "防护措施"
        WAF[Web应用防火墙]
        RATE_LIMIT[速率限制]
        MFA[多因子认证]
        ENCRYPTION[数据加密]
        RBAC[基于角色的访问控制]
        AUDIT[审计日志]
        NETWORK_POLICY[网络策略]
        IMAGE_SCAN[镜像扫描]
    end

    DDOS --> WAF
    BRUTE --> RATE_LIMIT
    PHISH --> MFA
    INJECTION --> WAF
    XSS --> WAF
    AUTH_BYPASS --> MFA
    DATA_LEAK --> ENCRYPTION
    PRIV_ESC --> RBAC
    INSIDER --> AUDIT
    CONTAINER_ESC --> IMAGE_SCAN
    K8S_MISC --> NETWORK_POLICY
    NETWORK_SNIFF --> ENCRYPTION
```

## 2. 认证安全架构

### 2.1 多因子认证(MFA)流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant C as 客户端
    participant A as 认证服务
    participant M as MFA服务
    participant D as 数据库
    participant N as 通知服务

    U->>C: 1. 输入用户名密码
    C->>A: 2. 提交认证请求
    A->>D: 3. 验证用户凭据
    D->>A: 4. 返回用户信息
    
    alt MFA已启用
        A->>M: 5. 请求MFA验证
        M->>N: 6. 发送验证码
        N->>U: 7. 短信/邮件验证码
        U->>C: 8. 输入验证码
        C->>M: 9. 提交验证码
        M->>M: 10. 验证TOTP/SMS码
        M->>A: 11. 返回MFA结果
    end
    
    A->>A: 12. 生成JWT令牌
    A->>D: 13. 记录登录日志
    A->>C: 14. 返回访问令牌
    C->>U: 15. 登录成功
```

### 2.2 JWT令牌安全设计
```java
@Component
public class SecureJwtTokenProvider {
    
    private final RSAKey rsaKey;
    private final JwtEncoder jwtEncoder;
    private final RedisTemplate<String, String> redisTemplate;
    
    public String generateAccessToken(Authentication authentication, String tenantId) {
        Instant now = Instant.now();
        Instant expiry = now.plus(1, ChronoUnit.HOURS);
        
        // 生成唯一的JTI（JWT ID）用于令牌撤销
        String jti = UUID.randomUUID().toString();
        
        JwtClaimsSet claims = JwtClaimsSet.builder()
            .issuer("https://auth.example.com")
            .subject(authentication.getName())
            .audience(Arrays.asList("web-client", "mobile-client"))
            .issuedAt(now)
            .expiresAt(expiry)
            .id(jti) // JWT ID
            .claim("tenant_id", tenantId)
            .claim("scope", getScopes(authentication))
            .claim("authorities", getAuthorities(authentication))
            .claim("session_id", getSessionId(authentication))
            .build();
        
        String token = jwtEncoder.encode(JwtEncoderParameters.from(claims)).getTokenValue();
        
        // 将JTI存储到Redis用于令牌撤销检查
        redisTemplate.opsForValue().set(
            "jwt:jti:" + jti, 
            authentication.getName(), 
            Duration.ofHours(1)
        );
        
        return token;
    }
    
    public boolean isTokenRevoked(String jti) {
        return !redisTemplate.hasKey("jwt:jti:" + jti);
    }
    
    public void revokeToken(String jti) {
        redisTemplate.delete("jwt:jti:" + jti);
    }
}
```

### 2.3 密码安全策略
```java
@Component
public class PasswordSecurityService {
    
    private final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder(12);
    private final RedisTemplate<String, String> redisTemplate;
    
    public void validatePasswordPolicy(String password, String username) {
        PasswordPolicy policy = getPasswordPolicy();
        
        // 长度检查
        if (password.length() < policy.getMinLength()) {
            throw new WeakPasswordException("密码长度不能少于" + policy.getMinLength() + "位");
        }
        
        // 复杂度检查
        if (policy.isRequireUppercase() && !password.matches(".*[A-Z].*")) {
            throw new WeakPasswordException("密码必须包含大写字母");
        }
        
        if (policy.isRequireLowercase() && !password.matches(".*[a-z].*")) {
            throw new WeakPasswordException("密码必须包含小写字母");
        }
        
        if (policy.isRequireNumbers() && !password.matches(".*[0-9].*")) {
            throw new WeakPasswordException("密码必须包含数字");
        }
        
        if (policy.isRequireSymbols() && !password.matches(".*[!@#$%^&*()_+\\-=\\[\\]{};':\"\\\\|,.<>\\/?].*")) {
            throw new WeakPasswordException("密码必须包含特殊字符");
        }
        
        // 常见密码检查
        if (isCommonPassword(password)) {
            throw new WeakPasswordException("密码过于常见，请使用更复杂的密码");
        }
        
        // 用户名相似性检查
        if (isSimilarToUsername(password, username)) {
            throw new WeakPasswordException("密码不能与用户名相似");
        }
    }
    
    public void checkPasswordHistory(String userId, String newPassword) {
        List<String> passwordHistory = getPasswordHistory(userId);
        
        for (String oldPasswordHash : passwordHistory) {
            if (passwordEncoder.matches(newPassword, oldPasswordHash)) {
                throw new WeakPasswordException("不能使用最近使用过的密码");
            }
        }
    }
    
    public void recordFailedLogin(String username, String ipAddress) {
        String key = "failed_login:" + username + ":" + ipAddress;
        String countStr = redisTemplate.opsForValue().get(key);
        int count = countStr != null ? Integer.parseInt(countStr) : 0;
        
        count++;
        redisTemplate.opsForValue().set(key, String.valueOf(count), Duration.ofMinutes(15));
        
        // 超过5次失败，锁定账户
        if (count >= 5) {
            lockAccount(username, Duration.ofMinutes(30));
        }
    }
    
    private void lockAccount(String username, Duration lockDuration) {
        redisTemplate.opsForValue().set(
            "account_locked:" + username, 
            "true", 
            lockDuration
        );
    }
}
```

## 3. 数据安全架构

### 3.1 数据加密策略
```java
@Configuration
public class DataEncryptionConfig {
    
    @Bean
    public AESUtil aesUtil() {
        return new AESUtil(getEncryptionKey());
    }
    
    @Bean
    public FieldEncryptor fieldEncryptor() {
        return new FieldEncryptor(aesUtil());
    }
    
    private String getEncryptionKey() {
        // 从环境变量或密钥管理服务获取加密密钥
        return System.getenv("DATA_ENCRYPTION_KEY");
    }
}

@Entity
@Table(name = "users")
public class User {
    
    @Id
    private UUID id;
    
    private String username;
    
    @Convert(converter = EncryptedStringConverter.class)
    private String email; // 加密存储
    
    @Convert(converter = EncryptedStringConverter.class)
    private String phone; // 加密存储
    
    private String passwordHash;
    
    @Convert(converter = EncryptedStringConverter.class)
    private String mfaSecret; // 加密存储
    
    // getters and setters
}

@Converter
public class EncryptedStringConverter implements AttributeConverter<String, String> {
    
    @Autowired
    private AESUtil aesUtil;
    
    @Override
    public String convertToDatabaseColumn(String attribute) {
        if (attribute == null) {
            return null;
        }
        return aesUtil.encrypt(attribute);
    }
    
    @Override
    public String convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        return aesUtil.decrypt(dbData);
    }
}
```

### 3.2 数据库安全配置
```sql
-- PostgreSQL安全配置

-- 启用行级安全
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_tenants ENABLE ROW LEVEL SECURITY;

-- 创建安全策略
CREATE POLICY tenant_isolation_policy ON users
    FOR ALL
    TO application_role
    USING (tenant_id = current_setting('app.current_tenant_id')::uuid);

CREATE POLICY user_data_access_policy ON users
    FOR SELECT
    TO application_role
    USING (
        id = current_setting('app.current_user_id')::uuid OR
        EXISTS (
            SELECT 1 FROM user_tenants ut
            WHERE ut.user_id = current_setting('app.current_user_id')::uuid
            AND ut.tenant_id = users.tenant_id
            AND ut.role IN ('admin', 'user_manager')
        )
    );

-- 创建审计触发器
CREATE OR REPLACE FUNCTION audit_trigger_function()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_logs (table_name, operation, new_data, user_id, timestamp)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(NEW), current_setting('app.current_user_id')::uuid, NOW());
        RETURN NEW;
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_logs (table_name, operation, old_data, new_data, user_id, timestamp)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), row_to_json(NEW), current_setting('app.current_user_id')::uuid, NOW());
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_logs (table_name, operation, old_data, user_id, timestamp)
        VALUES (TG_TABLE_NAME, TG_OP, row_to_json(OLD), current_setting('app.current_user_id')::uuid, NOW());
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 为敏感表创建审计触发器
CREATE TRIGGER users_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON users
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();

CREATE TRIGGER user_tenants_audit_trigger
    AFTER INSERT OR UPDATE OR DELETE ON user_tenants
    FOR EACH ROW EXECUTE FUNCTION audit_trigger_function();
```

## 4. 网络安全架构

### 4.1 Kubernetes网络策略
```yaml
# 默认拒绝所有入站流量
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: default-deny-ingress
  namespace: auth-system
spec:
  podSelector: {}
  policyTypes:
  - Ingress

---
# 允许认证服务访问数据库
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: auth-service-to-db
  namespace: auth-system
spec:
  podSelector:
    matchLabels:
      app: postgres
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: auth-service
    - podSelector:
        matchLabels:
          app: user-service
    - podSelector:
        matchLabels:
          app: tenant-service
    ports:
    - protocol: TCP
      port: 5432

---
# 允许服务访问Redis
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: services-to-redis
  namespace: auth-system
spec:
  podSelector:
    matchLabels:
      app: redis
  policyTypes:
  - Ingress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: auth-service
    - podSelector:
        matchLabels:
          app: permission-service
    ports:
    - protocol: TCP
      port: 6379

---
# 允许Ingress访问服务
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: ingress-to-services
  namespace: auth-system
spec:
  podSelector:
    matchLabels:
      tier: backend
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 8080
```

### 4.2 TLS/SSL配置
```yaml
# TLS证书配置
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx

---
# 强制HTTPS重定向
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: auth-ingress
  namespace: auth-system
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
    nginx.ingress.kubernetes.io/ssl-ciphers: "ECDHE-RSA-AES128-GCM-SHA256,ECDHE-RSA-AES256-GCM-SHA384"
spec:
  tls:
  - hosts:
    - auth.example.com
    secretName: auth-tls
  rules:
  - host: auth.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: auth-service
            port:
              number: 80
```

## 5. 容器安全架构

### 5.1 安全的Dockerfile
```dockerfile
# 使用非root用户运行
FROM openjdk:17-jre-slim

# 创建应用用户
RUN groupadd -r appuser && useradd -r -g appuser appuser

# 设置工作目录
WORKDIR /app

# 复制应用文件
COPY --chown=appuser:appuser target/auth-service.jar app.jar

# 移除不必要的包
RUN apt-get update && \
    apt-get install -y --no-install-recommends curl && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# 设置安全的文件权限
RUN chmod 755 /app && \
    chmod 644 /app/app.jar

# 切换到非root用户
USER appuser

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 5.2 Pod安全策略
```yaml
apiVersion: v1
kind: Pod
metadata:
  name: auth-service
  namespace: auth-system
spec:
  securityContext:
    runAsNonRoot: true
    runAsUser: 1000
    runAsGroup: 1000
    fsGroup: 1000
    seccompProfile:
      type: RuntimeDefault
  containers:
  - name: auth-service
    image: auth-service:latest
    securityContext:
      allowPrivilegeEscalation: false
      readOnlyRootFilesystem: true
      runAsNonRoot: true
      runAsUser: 1000
      capabilities:
        drop:
        - ALL
    resources:
      requests:
        memory: "512Mi"
        cpu: "250m"
      limits:
        memory: "1Gi"
        cpu: "500m"
    volumeMounts:
    - name: tmp-volume
      mountPath: /tmp
    - name: cache-volume
      mountPath: /app/cache
  volumes:
  - name: tmp-volume
    emptyDir: {}
  - name: cache-volume
    emptyDir: {}
```

## 6. 安全监控和审计

### 6.1 安全事件监控
```java
@Component
public class SecurityEventMonitor {

    private final MeterRegistry meterRegistry;
    private final ApplicationEventPublisher eventPublisher;

    // 安全指标
    private final Counter loginAttempts;
    private final Counter loginFailures;
    private final Counter suspiciousActivities;
    private final Counter privilegeEscalations;

    public SecurityEventMonitor(MeterRegistry meterRegistry, ApplicationEventPublisher eventPublisher) {
        this.meterRegistry = meterRegistry;
        this.eventPublisher = eventPublisher;

        this.loginAttempts = Counter.builder("security.login.attempts")
            .description("Total login attempts")
            .register(meterRegistry);

        this.loginFailures = Counter.builder("security.login.failures")
            .description("Failed login attempts")
            .register(meterRegistry);

        this.suspiciousActivities = Counter.builder("security.suspicious.activities")
            .description("Suspicious activities detected")
            .register(meterRegistry);

        this.privilegeEscalations = Counter.builder("security.privilege.escalations")
            .description("Privilege escalation attempts")
            .register(meterRegistry);
    }

    @EventListener
    public void handleLoginAttempt(LoginAttemptEvent event) {
        loginAttempts.increment(
            Tags.of(
                "username", event.getUsername(),
                "ip_address", event.getIpAddress(),
                "user_agent", event.getUserAgent()
            )
        );

        // 检测异常登录模式
        if (isAnomalousLogin(event)) {
            suspiciousActivities.increment();
            eventPublisher.publishEvent(new SuspiciousActivityEvent(event));
        }
    }

    @EventListener
    public void handleLoginFailure(LoginFailureEvent event) {
        loginFailures.increment(
            Tags.of(
                "username", event.getUsername(),
                "reason", event.getFailureReason(),
                "ip_address", event.getIpAddress()
            )
        );

        // 检测暴力破解攻击
        if (isBruteForceAttack(event)) {
            eventPublisher.publishEvent(new BruteForceAttackEvent(event));
        }
    }

    @EventListener
    public void handlePrivilegeChange(PrivilegeChangeEvent event) {
        if (isPrivilegeEscalation(event)) {
            privilegeEscalations.increment();
            eventPublisher.publishEvent(new PrivilegeEscalationEvent(event));
        }
    }

    private boolean isAnomalousLogin(LoginAttemptEvent event) {
        // 检测异常登录模式：
        // 1. 异地登录
        // 2. 异常时间登录
        // 3. 新设备登录
        // 4. 异常用户代理
        return checkGeolocationAnomaly(event) ||
               checkTimeAnomaly(event) ||
               checkDeviceAnomaly(event) ||
               checkUserAgentAnomaly(event);
    }

    private boolean isBruteForceAttack(LoginFailureEvent event) {
        // 检测暴力破解：
        // 1. 短时间内多次失败
        // 2. 同一IP多个账户失败
        // 3. 字典攻击模式
        return checkFailureRate(event) ||
               checkMultiAccountFailure(event) ||
               checkDictionaryAttack(event);
    }
}
```

### 6.2 实时安全告警
```yaml
# Prometheus告警规则
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: auth-security-alerts
  namespace: monitoring
spec:
  groups:
  - name: auth.security
    rules:
    # 登录失败率过高
    - alert: HighLoginFailureRate
      expr: rate(security_login_failures_total[5m]) > 0.1
      for: 2m
      labels:
        severity: warning
        service: auth-system
      annotations:
        summary: "High login failure rate detected"
        description: "Login failure rate is {{ $value }} failures per second"

    # 暴力破解攻击
    - alert: BruteForceAttack
      expr: increase(security_login_failures_total[1m]) > 10
      for: 1m
      labels:
        severity: critical
        service: auth-system
      annotations:
        summary: "Potential brute force attack detected"
        description: "{{ $value }} login failures in the last minute"

    # 可疑活动增加
    - alert: SuspiciousActivitySpike
      expr: rate(security_suspicious_activities_total[5m]) > 0.05
      for: 3m
      labels:
        severity: warning
        service: auth-system
      annotations:
        summary: "Spike in suspicious activities"
        description: "Suspicious activity rate is {{ $value }} per second"

    # 权限提升尝试
    - alert: PrivilegeEscalationAttempt
      expr: increase(security_privilege_escalations_total[5m]) > 0
      for: 0m
      labels:
        severity: critical
        service: auth-system
      annotations:
        summary: "Privilege escalation attempt detected"
        description: "{{ $value }} privilege escalation attempts in the last 5 minutes"

    # 服务不可用
    - alert: AuthServiceDown
      expr: up{job="auth-service"} == 0
      for: 1m
      labels:
        severity: critical
        service: auth-system
      annotations:
        summary: "Auth service is down"
        description: "Auth service has been down for more than 1 minute"
```

### 6.3 安全审计日志
```java
@Component
public class SecurityAuditLogger {

    private final Logger securityLogger = LoggerFactory.getLogger("SECURITY");
    private final ObjectMapper objectMapper;

    @EventListener
    @Async
    public void logSecurityEvent(SecurityEvent event) {
        try {
            SecurityAuditLog auditLog = SecurityAuditLog.builder()
                .timestamp(Instant.now())
                .eventType(event.getEventType())
                .userId(event.getUserId())
                .tenantId(event.getTenantId())
                .ipAddress(event.getIpAddress())
                .userAgent(event.getUserAgent())
                .resource(event.getResource())
                .action(event.getAction())
                .result(event.getResult())
                .details(event.getDetails())
                .riskLevel(calculateRiskLevel(event))
                .build();

            String logJson = objectMapper.writeValueAsString(auditLog);
            securityLogger.info(logJson);

            // 高风险事件立即发送告警
            if (auditLog.getRiskLevel() == RiskLevel.HIGH) {
                sendSecurityAlert(auditLog);
            }

        } catch (Exception e) {
            securityLogger.error("Failed to log security event", e);
        }
    }

    private RiskLevel calculateRiskLevel(SecurityEvent event) {
        // 根据事件类型、用户行为、时间等因素计算风险等级
        int riskScore = 0;

        // 事件类型风险评分
        switch (event.getEventType()) {
            case LOGIN_FAILURE:
                riskScore += 2;
                break;
            case PRIVILEGE_ESCALATION:
                riskScore += 8;
                break;
            case DATA_ACCESS:
                riskScore += 3;
                break;
            case CONFIGURATION_CHANGE:
                riskScore += 5;
                break;
        }

        // 异常行为评分
        if (isAnomalousLocation(event)) riskScore += 3;
        if (isAnomalousTime(event)) riskScore += 2;
        if (isNewDevice(event)) riskScore += 2;

        // 用户角色评分
        if (isPrivilegedUser(event.getUserId())) riskScore += 2;

        if (riskScore >= 8) return RiskLevel.HIGH;
        if (riskScore >= 5) return RiskLevel.MEDIUM;
        return RiskLevel.LOW;
    }
}
```

## 7. 密钥管理和轮换

### 7.1 密钥管理服务
```java
@Service
public class KeyManagementService {

    private final VaultTemplate vaultTemplate;
    private final RedisTemplate<String, String> redisTemplate;

    public String getEncryptionKey(String keyId) {
        // 从Vault获取加密密钥
        VaultResponse response = vaultTemplate.read("secret/data/encryption-keys/" + keyId);
        if (response != null && response.getData() != null) {
            return (String) response.getData().get("key");
        }
        throw new KeyNotFoundException("Encryption key not found: " + keyId);
    }

    public RSAKey getJwtSigningKey() {
        // 从Vault获取JWT签名密钥
        VaultResponse response = vaultTemplate.read("secret/data/jwt-keys/current");
        if (response != null && response.getData() != null) {
            String privateKey = (String) response.getData().get("private_key");
            String publicKey = (String) response.getData().get("public_key");
            return RSAKey.parse(privateKey);
        }
        throw new KeyNotFoundException("JWT signing key not found");
    }

    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void rotateJwtKeys() {
        try {
            // 生成新的RSA密钥对
            KeyPair keyPair = generateRSAKeyPair();
            RSAPublicKey publicKey = (RSAPublicKey) keyPair.getPublic();
            RSAPrivateKey privateKey = (RSAPrivateKey) keyPair.getPrivate();

            // 创建新的RSA密钥
            String keyId = UUID.randomUUID().toString();
            RSAKey rsaKey = new RSAKey.Builder(publicKey)
                .privateKey(privateKey)
                .keyID(keyId)
                .build();

            // 保存到Vault
            Map<String, Object> keyData = Map.of(
                "key_id", keyId,
                "private_key", rsaKey.toJSONString(),
                "public_key", rsaKey.toPublicJWK().toJSONString(),
                "created_at", Instant.now().toString()
            );

            vaultTemplate.write("secret/data/jwt-keys/" + keyId, keyData);

            // 更新当前密钥引用
            vaultTemplate.write("secret/data/jwt-keys/current", keyData);

            // 通知所有服务更新密钥
            notifyKeyRotation(keyId);

        } catch (Exception e) {
            log.error("Failed to rotate JWT keys", e);
        }
    }

    @Scheduled(cron = "0 0 3 * * ?") // 每天凌晨3点执行
    public void rotateEncryptionKeys() {
        try {
            // 生成新的AES密钥
            String newKeyId = UUID.randomUUID().toString();
            String newKey = generateAESKey();

            // 保存新密钥到Vault
            Map<String, Object> keyData = Map.of(
                "key", newKey,
                "created_at", Instant.now().toString()
            );
            vaultTemplate.write("secret/data/encryption-keys/" + newKeyId, keyData);

            // 更新当前密钥引用
            vaultTemplate.write("secret/data/encryption-keys/current",
                Map.of("key_id", newKeyId));

            // 重新加密敏感数据
            reencryptSensitiveData(newKeyId);

        } catch (Exception e) {
            log.error("Failed to rotate encryption keys", e);
        }
    }
}
```

### 7.2 Vault配置
```yaml
# Vault部署配置
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: vault
  namespace: security
spec:
  serviceName: vault
  replicas: 3
  selector:
    matchLabels:
      app: vault
  template:
    metadata:
      labels:
        app: vault
    spec:
      containers:
      - name: vault
        image: vault:1.15.0
        ports:
        - containerPort: 8200
        env:
        - name: VAULT_DEV_ROOT_TOKEN_ID
          valueFrom:
            secretKeyRef:
              name: vault-secrets
              key: root-token
        - name: VAULT_DEV_LISTEN_ADDRESS
          value: "0.0.0.0:8200"
        volumeMounts:
        - name: vault-config
          mountPath: /vault/config
        - name: vault-data
          mountPath: /vault/data
        command:
        - vault
        - server
        - -config=/vault/config/vault.hcl
        securityContext:
          capabilities:
            add:
            - IPC_LOCK
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
      volumes:
      - name: vault-config
        configMap:
          name: vault-config
  volumeClaimTemplates:
  - metadata:
      name: vault-data
    spec:
      accessModes: ["ReadWriteOnce"]
      resources:
        requests:
          storage: 10Gi
```

## 8. 安全合规和最佳实践

### 8.1 GDPR合规配置
```java
@Service
public class GDPRComplianceService {

    private final UserRepository userRepository;
    private final AuditLogRepository auditLogRepository;

    public void handleDataSubjectRequest(DataSubjectRequest request) {
        switch (request.getType()) {
            case ACCESS:
                handleDataAccessRequest(request);
                break;
            case RECTIFICATION:
                handleDataRectificationRequest(request);
                break;
            case ERASURE:
                handleDataErasureRequest(request);
                break;
            case PORTABILITY:
                handleDataPortabilityRequest(request);
                break;
        }
    }

    private void handleDataErasureRequest(DataSubjectRequest request) {
        String userId = request.getUserId();

        // 检查数据保留要求
        if (hasLegalRetentionRequirement(userId)) {
            throw new DataRetentionException("数据需要根据法律要求保留");
        }

        // 匿名化用户数据
        User user = userRepository.findById(userId);
        user.setEmail(anonymizeEmail(user.getEmail()));
        user.setPhone(anonymizePhone(user.getPhone()));
        user.setName("已删除用户");
        user.setStatus(UserStatus.DELETED);
        userRepository.save(user);

        // 记录删除操作
        auditLogRepository.save(AuditLog.builder()
            .action("DATA_ERASURE")
            .userId(userId)
            .details("User data anonymized per GDPR request")
            .timestamp(Instant.now())
            .build());
    }

    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void enforceDataRetentionPolicy() {
        // 删除超过保留期的审计日志
        Instant cutoffDate = Instant.now().minus(Duration.ofDays(2555)); // 7年
        auditLogRepository.deleteByTimestampBefore(cutoffDate);

        // 匿名化非活跃用户数据
        Instant inactiveDate = Instant.now().minus(Duration.ofDays(1095)); // 3年
        List<User> inactiveUsers = userRepository.findInactiveUsers(inactiveDate);
        for (User user : inactiveUsers) {
            anonymizeUserData(user);
        }
    }
}
```

### 8.2 安全配置检查清单
```yaml
# 安全配置检查清单
apiVersion: v1
kind: ConfigMap
metadata:
  name: security-checklist
  namespace: auth-system
data:
  checklist.md: |
    # 安全配置检查清单

    ## 认证安全
    - [ ] 强制使用HTTPS
    - [ ] 实施多因子认证(MFA)
    - [ ] 配置密码复杂度策略
    - [ ] 启用账户锁定机制
    - [ ] 实施JWT令牌撤销
    - [ ] 配置会话超时

    ## 数据安全
    - [ ] 敏感数据加密存储
    - [ ] 数据传输加密(TLS 1.3)
    - [ ] 实施数据备份加密
    - [ ] 配置数据库访问控制
    - [ ] 启用数据库审计日志
    - [ ] 实施数据脱敏

    ## 网络安全
    - [ ] 配置网络策略
    - [ ] 实施入侵检测
    - [ ] 配置防火墙规则
    - [ ] 启用DDoS防护
    - [ ] 实施API速率限制
    - [ ] 配置安全头部

    ## 容器安全
    - [ ] 使用非root用户运行
    - [ ] 扫描容器镜像漏洞
    - [ ] 配置资源限制
    - [ ] 启用只读根文件系统
    - [ ] 禁用特权容器
    - [ ] 配置安全上下文

    ## 监控和审计
    - [ ] 配置安全事件监控
    - [ ] 实施实时告警
    - [ ] 启用审计日志
    - [ ] 配置日志聚合
    - [ ] 实施异常检测
    - [ ] 定期安全评估

    ## 合规性
    - [ ] GDPR合规配置
    - [ ] 数据保留策略
    - [ ] 隐私政策实施
    - [ ] 安全培训完成
    - [ ] 事件响应计划
    - [ ] 定期安全审计
```

这个安全架构设计为统一认证授权系统提供了全面的安全防护，包括认证安全、数据安全、网络安全、容器安全、监控审计、密钥管理和合规性等多个层面，确保系统在各种威胁下的安全性和可靠性。通过实施这些安全措施，系统能够有效防范各种安全威胁，满足企业级安全要求和合规性标准。
