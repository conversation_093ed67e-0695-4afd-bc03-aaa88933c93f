# 统一认证授权系统 - 架构设计变更日志

## 版本 2.0 - 2024-07-30

### 🔧 重大架构修正

#### 1. 两级认证体系修正
**问题**: 原架构设计与PRD需求不符，用户被强制绑定到租户
**修正**: 
- ✅ 用户表去掉`tenant_id`字段，用户是全局的
- ✅ 通过`user_tenants`关系表管理用户与租户的多对多关系
- ✅ 支持用户在多个租户间自由切换
- ✅ 认证流程分为用户认证和租户选择两个阶段

#### 2. 数据库设计优化
**新增**: 采用PostgreSQL继承表按月分表策略
**优化内容**:
- ✅ 审计日志表(`audit_logs`)采用继承表按月自动分表
- ✅ 通知记录表(`notifications`)采用继承表按月自动分表
- ✅ 自动创建当月和下月的分表
- ✅ 自动清理过期数据和旧分表
- ✅ 提供完整的分表管理函数和监控查询

#### 3. 文档规范化
**新增**: 按照软件工程标准为所有文档添加编号前缀
**文档结构**:
```
00-项目架构总览.md           - 整体架构总览
01-PRD-统一认证授权系统.md    - 产品需求文档
02-系统架构设计.md           - 系统架构设计
03-技术架构详细设计.md       - 技术架构详细设计
04-数据库设计-PostgreSQL17.md - 数据库设计（含继承表分表）
05-API设计规范.md           - API设计规范
06-部署架构设计.md           - 部署架构设计
07-安全架构设计.md           - 安全架构设计
README.md                   - 文档索引和导航
CHANGELOG.md               - 变更日志
```

### 📊 数据库架构亮点

#### 继承表分表策略
```sql
-- 主表定义
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID,
    user_id UUID,
    action VARCHAR(100) NOT NULL,
    -- 其他字段...
    timestamp TIMESTAMPTZ DEFAULT NOW(),
    created_month DATE GENERATED ALWAYS AS (date_trunc('month', timestamp)::date) STORED
);

-- 自动创建月表函数
CREATE OR REPLACE FUNCTION create_audit_logs_table(target_date DATE)
RETURNS TEXT AS $$
-- 函数实现...
$$;

-- 自动路由插入触发器
CREATE TRIGGER audit_logs_insert_trigger
    BEFORE INSERT ON audit_logs
    FOR EACH ROW EXECUTE FUNCTION audit_logs_insert_trigger();
```

#### 自动化维护
- 🔄 每月自动创建下个月的分表
- 🗑️ 自动清理过期数据（审计日志保留24个月，通知记录保留6个月）
- 📊 提供分表统计和性能监控函数
- ⚡ 支持跨月查询和数据聚合

### 🔑 两级认证体系实现

#### 数据模型
```sql
-- 全局用户表（无租户依赖）
CREATE TABLE users (
    id UUID PRIMARY KEY,
    username VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE,
    -- 无tenant_id字段
);

-- 用户租户关系表（核心）
CREATE TABLE user_tenants (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    tenant_id UUID REFERENCES tenants(id),
    role VARCHAR(50) NOT NULL,
    status user_tenant_status_enum DEFAULT 'pending',
    is_default BOOLEAN DEFAULT FALSE,
    UNIQUE(user_id, tenant_id)
);
```

#### 认证流程
1. **第一级：用户认证**
   - 验证用户名密码
   - 多因子认证（如果启用）
   - 生成临时认证状态

2. **第二级：租户选择**
   - 获取用户所属租户列表
   - 用户选择目标租户（多租户用户）
   - 加载租户上下文和权限
   - 生成包含租户信息的JWT令牌

#### 关键API
```http
# 获取用户租户列表
GET /api/v1/users/tenants

# 切换租户上下文
POST /api/v1/users/switch-tenant
{
  "tenant_id": "tenant-uuid"
}

# OAuth令牌包含租户上下文
{
  "access_token": "...",
  "tenant_context": {
    "tenant_id": "tenant-uuid",
    "tenant_name": "企业名称",
    "user_role": "admin",
    "permissions": ["user:read", "user:write"]
  }
}
```

### 🚀 技术架构优化

#### 上下文管理
```java
// 两级认证上下文
@Component
public class AuthenticationContext {
    private static final ThreadLocal<String> currentUser = new ThreadLocal<>();
    private static final ThreadLocal<String> currentTenant = new ThreadLocal<>();
    
    // 支持用户和租户上下文的独立管理
}
```

#### 权限验证
```java
// 基于用户+租户的权限验证
@PreAuthorize("@permissionService.hasPermission(authentication.name, #tenantId, 'user:read')")
public UserDto getUser(String userId, String tenantId) {
    // 实现逻辑
}
```

### 📈 性能优化

#### 数据库优化
- 🔍 合理的索引设计，支持多租户查询
- 📊 分表策略减少单表数据量
- ⚡ 行级安全策略优化
- 🔄 自动统计信息更新

#### 缓存策略
- 🏃‍♂️ 用户权限缓存（按租户隔离）
- 🔄 租户配置缓存
- ⚡ JWT令牌黑名单缓存

### 🛡️ 安全增强

#### 多租户数据隔离
```sql
-- 行级安全策略
CREATE POLICY user_tenants_tenant_isolation ON user_tenants
    FOR ALL TO application_role
    USING (
        tenant_id = current_setting('app.current_tenant_id')::uuid OR
        user_id = current_setting('app.current_user_id')::uuid
    );
```

#### 审计增强
- 📝 完整的操作审计日志
- 🔍 跨租户操作追踪
- 📊 安全事件监控
- 🚨 异常行为检测

### 🔧 运维支持

#### 自动化维护
```sql
-- 数据库维护任务
CREATE OR REPLACE FUNCTION database_maintenance()
RETURNS TEXT AS $$
-- 自动创建分表、清理过期数据、更新统计信息
$$;

-- 定时任务（需要pg_cron扩展）
SELECT cron.schedule('monthly-maintenance', '0 2 1 * *', 'SELECT database_maintenance();');
```

#### 监控查询
- 📊 分表统计信息
- 🔍 性能监控查询
- 🚨 异常检测查询
- 📈 容量规划支持

### 📋 业务场景支持

修正后的架构完全支持PRD中的所有业务场景：

1. ✅ **用户自由注册** - 全局用户，无租户依赖
2. ✅ **用户创建租户** - 自动成为租户管理员
3. ✅ **管理员邀请用户** - 支持邀请任何邮箱用户
4. ✅ **用户申请加入租户** - 支持申请审批流程
5. ✅ **多租户切换** - 用户可在不同租户间自由切换
6. ✅ **不同权限** - 用户在不同租户中有不同角色和权限

### 🎯 下一步计划

1. **代码实现** - 基于架构设计实现核心功能
2. **测试验证** - 编写单元测试和集成测试
3. **性能测试** - 验证分表策略和缓存效果
4. **安全测试** - 验证多租户数据隔离和权限控制
5. **部署验证** - 在测试环境验证部署架构

---

*本次修正确保了架构设计与PRD需求的完全匹配，为后续的开发实现提供了坚实的基础。*
