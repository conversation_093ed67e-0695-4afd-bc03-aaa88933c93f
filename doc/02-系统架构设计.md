# 02-统一认证授权系统 - 系统架构设计

## 1. 架构概述

### 1.1 设计原则
- **两级认证体系**: 用户级认证 + 租户级认证，用户可自由加入多个租户
- **微服务架构**: 采用微服务架构，实现服务解耦和独立部署
- **前后端分离**: 前端采用现代化框架，后端提供RESTful API
- **安全优先**: 基于Spring Security和Spring Authorization Server构建安全可靠的认证授权体系
- **高可用性**: 支持水平扩展，具备容错和故障恢复能力
- **标准协议**: 支持OAuth 2.0、OpenID Connect、SAML 2.0等标准协议

### 1.2 技术栈选择

#### 后端技术栈
- **框架**: Spring Boot 3.x + Spring Security 6.x + Spring Authorization Server 1.x
- **构建工具**: Maven 3.8+
- **数据库**: PostgreSQL 17 (主数据库) + Redis 7.0 (缓存)
  - **PostgreSQL 17优势**:
    - 原生支持行级安全(RLS)，完美适配多租户架构
    - JSONB数据类型，灵活存储权限配置和用户偏好
    - 强大的并发控制和ACID事务支持
    - 内置全文搜索和审计功能
    - 企业级安全特性和权限管理
- **消息队列**: RabbitMQ 3.12 (异步处理)
- **搜索引擎**: Elasticsearch 8.x (日志分析)
- **监控**: Micrometer + Prometheus + Grafana
- **文档**: SpringDoc OpenAPI 3.x

#### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design
- **状态管理**: Redux Toolkit
- **路由**: React Router 6
- **HTTP客户端**: Axios

#### 基础设施
- **容器化**: Docker + Docker Compose
- **编排**: Kubernetes (生产环境)
- **网关**: Spring Cloud Gateway
- **配置中心**: Spring Cloud Config
- **服务发现**: Spring Cloud Consul
- **负载均衡**: Nginx + HAProxy

## 2. 系统分层架构

```mermaid
graph TB
    subgraph "表现层 (Presentation Layer)"
        WEB[Web管理后台<br/>React + TypeScript]
        MOBILE[移动端应用<br/>React Native]
        API_DOC[API文档<br/>SpringDoc]
    end

    subgraph "网关层 (Gateway Layer)"
        GATEWAY[API网关<br/>Spring Cloud Gateway]
        LB[负载均衡<br/>Nginx]
    end

    subgraph "应用服务层 (Application Service Layer)"
        AUTH_SRV[认证服务<br/>Spring Authorization Server]
        USER_SRV[用户管理服务<br/>Spring Boot]
        TENANT_SRV[租户管理服务<br/>Spring Boot]
        PERM_SRV[权限管理服务<br/>Spring Boot]
        NOTIFY_SRV[通知服务<br/>Spring Boot]
        AUDIT_SRV[审计服务<br/>Spring Boot]
    end

    subgraph "业务逻辑层 (Business Logic Layer)"
        AUTH_BIZ[认证业务逻辑]
        USER_BIZ[用户业务逻辑]
        TENANT_BIZ[租户业务逻辑]
        PERM_BIZ[权限业务逻辑]
        MFA_BIZ[MFA业务逻辑]
    end

    subgraph "数据访问层 (Data Access Layer)"
        USER_DAO[用户数据访问<br/>Spring Data JPA]
        TENANT_DAO[租户数据访问<br/>Spring Data JPA]
        PERM_DAO[权限数据访问<br/>Spring Data JPA]
        CACHE_DAO[缓存访问<br/>Spring Data Redis]
    end

    subgraph "数据存储层 (Data Storage Layer)"
        POSTGRESQL[(PostgreSQL 17<br/>主数据库)]
        REDIS[(Redis 7.0<br/>缓存数据库)]
        ES[(Elasticsearch<br/>日志存储)]
        FILE_STORAGE[(MinIO<br/>文件存储)]
    end

    subgraph "外部服务层 (External Service Layer)"
        EMAIL[邮件服务<br/>SMTP]
        SMS[短信服务<br/>阿里云SMS]
        LDAP[LDAP/AD<br/>企业目录]
        THIRD_PARTY[第三方应用<br/>OAuth客户端]
    end

    %% 连接关系
    WEB --> GATEWAY
    MOBILE --> GATEWAY
    GATEWAY --> LB
    LB --> AUTH_SRV
    LB --> USER_SRV
    LB --> TENANT_SRV
    LB --> PERM_SRV
    LB --> NOTIFY_SRV
    LB --> AUDIT_SRV

    AUTH_SRV --> AUTH_BIZ
    USER_SRV --> USER_BIZ
    TENANT_SRV --> TENANT_BIZ
    PERM_SRV --> PERM_BIZ
    AUTH_SRV --> MFA_BIZ

    AUTH_BIZ --> USER_DAO
    AUTH_BIZ --> CACHE_DAO
    USER_BIZ --> USER_DAO
    USER_BIZ --> CACHE_DAO
    TENANT_BIZ --> TENANT_DAO
    PERM_BIZ --> PERM_DAO
    PERM_BIZ --> CACHE_DAO

    USER_DAO --> POSTGRESQL
    TENANT_DAO --> POSTGRESQL
    PERM_DAO --> POSTGRESQL
    CACHE_DAO --> REDIS
    AUDIT_SRV --> ES
    USER_SRV --> FILE_STORAGE

    NOTIFY_SRV --> EMAIL
    NOTIFY_SRV --> SMS
    USER_SRV --> LDAP
    AUTH_SRV --> THIRD_PARTY

    style WEB fill:#e3f2fd
    style MOBILE fill:#e3f2fd
    style AUTH_SRV fill:#fff3e0
    style USER_SRV fill:#fff3e0
    style TENANT_SRV fill:#fff3e0
    style PERM_SRV fill:#fff3e0
    style POSTGRESQL fill:#f3e5f5
    style REDIS fill:#e8f5e8
```

## 3. 核心服务模块设计

### 3.1 认证服务 (Authentication Service)
**技术栈**: Spring Authorization Server + Spring Security
**职责**:
- OAuth 2.0 / OpenID Connect 协议实现
- JWT令牌生成和验证
- 多因子认证(MFA)支持
- 单点登录(SSO)实现
- 会话管理

**核心组件**:
```java
@RestController
@RequestMapping("/oauth2")
public class AuthorizationController {
    // OAuth 2.0 授权端点
}

@Service
public class CustomUserDetailsService implements UserDetailsService {
    // 用户认证逻辑
}

@Component
public class MfaAuthenticationProvider implements AuthenticationProvider {
    // MFA认证提供者
}
```

### 3.2 用户管理服务 (User Management Service)
**技术栈**: Spring Boot + Spring Data JPA
**职责**:
- 用户注册、登录、注销
- 用户信息管理
- 密码策略管理
- 用户状态管理
- LDAP集成

### 3.3 租户管理服务 (Tenant Management Service)
**技术栈**: Spring Boot + Spring Data JPA
**职责**:
- 租户创建和配置
- 多租户数据隔离
- 租户用户关系管理
- 邀请和申请流程
- 租户品牌定制

### 3.4 权限管理服务 (Permission Management Service)
**技术栈**: Spring Boot + Spring Security
**职责**:
- RBAC权限模型实现
- 角色和权限管理
- 动态权限验证
- 权限缓存管理
- 资源访问控制

### 3.5 通知服务 (Notification Service)
**技术栈**: Spring Boot + RabbitMQ
**职责**:
- 邮件通知发送
- 短信验证码发送
- 系统消息推送
- 通知模板管理
- 异步消息处理

### 3.6 审计服务 (Audit Service)
**技术栈**: Spring Boot + Elasticsearch
**职责**:
- 用户行为审计
- 安全事件记录
- 登录日志管理
- 操作轨迹追踪
- 合规报告生成

## 4. 数据架构设计

### 4.1 数据库分层
```mermaid
graph TB
    subgraph "应用层"
        APP1[认证服务]
        APP2[用户服务]
        APP3[租户服务]
        APP4[权限服务]
    end

    subgraph "数据访问层"
        DAO1[用户DAO]
        DAO2[租户DAO]
        DAO3[权限DAO]
        CACHE[缓存层]
    end

    subgraph "数据存储层"
        PG_MASTER[(PostgreSQL主库)]
        PG_SLAVE[(PostgreSQL从库)]
        REDIS_CLUSTER[(Redis集群)]
        ES_CLUSTER[(ES集群)]
    end

    APP1 --> DAO1
    APP2 --> DAO1
    APP3 --> DAO2
    APP4 --> DAO3

    DAO1 --> CACHE
    DAO2 --> CACHE
    DAO3 --> CACHE

    CACHE --> REDIS_CLUSTER
    DAO1 --> PG_MASTER
    DAO2 --> PG_MASTER
    DAO3 --> PG_MASTER

    PG_MASTER --> PG_SLAVE
    
    APP1 --> ES_CLUSTER
    APP2 --> ES_CLUSTER
    APP3 --> ES_CLUSTER
    APP4 --> ES_CLUSTER
```

### 4.2 多租户数据隔离策略
- **共享数据库，共享Schema**: 通过tenant_id字段区分租户数据
- **行级安全(RLS)**: 使用PostgreSQL行级安全策略自动过滤租户数据
- **Schema级隔离**: 可选择为大租户创建独立Schema
- **缓存隔离**: Redis中使用租户前缀隔离缓存数据
- **连接池隔离**: 为不同租户配置独立的数据库连接池

## 5. 安全架构设计

### 5.1 两级认证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant A as 认证服务
    participant U as 用户服务
    participant T as 租户服务
    participant R as Redis

    Note over C,R: 第一级：用户认证
    C->>G: 1. 用户登录请求
    G->>A: 2. 转发认证请求
    A->>U: 3. 验证用户凭据
    U->>A: 4. 返回用户信息

    alt MFA启用
        A->>C: 5a. 要求MFA验证
        C->>A: 6a. 提交MFA验证码
        A->>A: 7a. 验证MFA
    end

    Note over C,R: 第二级：租户选择
    A->>T: 8. 获取用户租户列表
    T->>A: 9. 返回租户列表

    alt 用户属于多个租户
        A->>C: 10a. 返回租户选择页面
        C->>A: 11a. 选择目标租户
    else 用户只属于一个租户
        A->>A: 10b. 自动选择唯一租户
    end

    A->>T: 12. 验证用户在租户中的权限
    T->>A: 13. 返回租户角色权限
    A->>A: 14. 生成包含租户上下文的JWT令牌
    A->>R: 15. 缓存会话和租户上下文
    A->>C: 16. 返回访问令牌和租户信息
```

### 5.2 权限验证流程
```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant P as 权限服务
    participant R as Redis
    participant D as 数据库

    C->>G: 1. 携带JWT访问资源
    G->>G: 2. 验证JWT签名
    G->>P: 3. 权限验证请求
    P->>R: 4. 查询权限缓存
    alt 缓存命中
        R->>P: 5a. 返回权限信息
    else 缓存未命中
        P->>D: 5b. 查询数据库
        D->>P: 6b. 返回权限信息
        P->>R: 7b. 更新缓存
    end
    P->>G: 8. 返回权限验证结果
    G->>C: 9. 返回访问结果
```

## 6. 部署架构设计

### 6.1 容器化部署
```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  auth-service:
    image: auth-service:latest
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DATABASE_URL=*****************************************
    depends_on:
      - postgresql
      - redis

  user-service:
    image: user-service:latest
    ports:
      - "8081:8080"
    depends_on:
      - postgresql
      - redis

  postgresql:
    image: postgres:17
    environment:
      - POSTGRES_PASSWORD=password
      - POSTGRES_DB=auth_db
      - POSTGRES_USER=auth_user

  redis:
    image: redis:7.0
    ports:
      - "6379:6379"
```

### 6.2 Kubernetes部署架构
```mermaid
graph TB
    subgraph "Kubernetes集群"
        subgraph "Ingress层"
            INGRESS[Nginx Ingress]
        end
        
        subgraph "应用层"
            AUTH_POD[认证服务Pod]
            USER_POD[用户服务Pod]
            TENANT_POD[租户服务Pod]
            PERM_POD[权限服务Pod]
        end
        
        subgraph "数据层"
            PG_POD[PostgreSQL Pod]
            REDIS_POD[Redis Pod]
            ES_POD[Elasticsearch Pod]
        end

        subgraph "存储层"
            PV1[持久化卷1]
            PV2[持久化卷2]
            PV3[持久化卷3]
        end
    end

    INGRESS --> AUTH_POD
    INGRESS --> USER_POD
    INGRESS --> TENANT_POD
    INGRESS --> PERM_POD

    AUTH_POD --> PG_POD
    AUTH_POD --> REDIS_POD
    USER_POD --> PG_POD
    TENANT_POD --> PG_POD
    PERM_POD --> REDIS_POD

    PG_POD --> PV1
    REDIS_POD --> PV2
    ES_POD --> PV3
```

## 7. 监控和运维

### 7.1 监控体系
- **应用监控**: Micrometer + Prometheus + Grafana
- **日志监控**: ELK Stack (Elasticsearch + Logstash + Kibana)
- **链路追踪**: Spring Cloud Sleuth + Zipkin
- **健康检查**: Spring Boot Actuator

### 7.2 告警策略
- **系统指标**: CPU、内存、磁盘使用率
- **应用指标**: 响应时间、错误率、吞吐量
- **业务指标**: 登录成功率、认证失败次数
- **安全指标**: 异常登录、权限异常访问

## 8. 开发规范

### 8.1 代码结构
```
src/
├── main/
│   ├── java/
│   │   └── com/company/auth/
│   │       ├── config/          # 配置类
│   │       ├── controller/      # 控制器
│   │       ├── service/         # 服务层
│   │       ├── repository/      # 数据访问层
│   │       ├── entity/          # 实体类
│   │       ├── dto/             # 数据传输对象
│   │       ├── security/        # 安全配置
│   │       └── util/            # 工具类
│   └── resources/
│       ├── application.yml      # 配置文件
│       ├── db/migration/        # 数据库迁移脚本
│       └── templates/           # 邮件模板
└── test/                        # 测试代码
```

### 8.2 API设计规范
- **RESTful风格**: 使用标准HTTP方法和状态码
- **统一响应格式**: 包含code、message、data字段
- **版本控制**: 通过URL路径进行版本管理
- **文档生成**: 使用SpringDoc自动生成API文档

这个架构设计为统一认证授权系统提供了完整的技术框架，确保系统的安全性、可扩展性和可维护性。
